<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音乐功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #ff6b9d 0%, #c44569 50%, #f8b500 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0;
        }
        .test-container {
            background: rgba(255, 255, 255, 0.9);
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            max-width: 500px;
        }
        .music-controls {
            margin: 20px 0;
        }
        .btn {
            background: #ff6b9d;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            font-size: 1rem;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #e55a87;
            transform: translateY(-2px);
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        .status {
            margin: 15px 0;
            padding: 10px;
            border-radius: 10px;
            font-weight: bold;
        }
        .status.playing {
            background: rgba(76, 175, 80, 0.2);
            color: #2e7d32;
        }
        .status.paused {
            background: rgba(255, 152, 0, 0.2);
            color: #f57c00;
        }
        .status.error {
            background: rgba(244, 67, 54, 0.2);
            color: #c62828;
        }
        .volume-control {
            margin: 15px 0;
        }
        .volume-slider {
            width: 200px;
            margin: 0 10px;
        }
        .link {
            margin-top: 20px;
        }
        .link a {
            color: #ff6b9d;
            text-decoration: none;
            font-weight: bold;
        }
        .link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎵 音乐功能测试</h1>
        
        <div class="status" id="status">准备就绪</div>
        
        <div class="music-controls">
            <button class="btn" id="playBtn">▶️ 播放</button>
            <button class="btn" id="pauseBtn">⏸️ 暂停</button>
            <button class="btn" id="stopBtn">⏹️ 停止</button>
        </div>
        
        <div class="volume-control">
            <label>音量：</label>
            <input type="range" class="volume-slider" id="volumeSlider" min="0" max="1" step="0.1" value="0.3">
            <span id="volumeValue">30%</span>
        </div>
        
        <div>
            <p><strong>音频文件：</strong>assets/audio/romantic-musi.mp3</p>
            <p><strong>当前时间：</strong><span id="currentTime">0:00</span> / <span id="duration">0:00</span></p>
        </div>
        
        <div class="link">
            <a href="index.html">← 返回主应用</a>
        </div>
    </div>

    <audio id="testAudio" loop>
        <source src="assets/audio/romantic-musi.mp3" type="audio/mpeg">
        <source src="assets/audio/romantic-musi.mp3" type="audio/mp3">
        您的浏览器不支持音频播放。
    </audio>

    <script>
        const audio = document.getElementById('testAudio');
        const status = document.getElementById('status');
        const playBtn = document.getElementById('playBtn');
        const pauseBtn = document.getElementById('pauseBtn');
        const stopBtn = document.getElementById('stopBtn');
        const volumeSlider = document.getElementById('volumeSlider');
        const volumeValue = document.getElementById('volumeValue');
        const currentTime = document.getElementById('currentTime');
        const duration = document.getElementById('duration');

        // 更新状态显示
        function updateStatus(message, type = '') {
            status.textContent = message;
            status.className = 'status ' + type;
        }

        // 格式化时间
        function formatTime(seconds) {
            const mins = Math.floor(seconds / 60);
            const secs = Math.floor(seconds % 60);
            return `${mins}:${secs.toString().padStart(2, '0')}`;
        }

        // 播放按钮
        playBtn.addEventListener('click', async () => {
            try {
                await audio.play();
                updateStatus('正在播放...', 'playing');
            } catch (error) {
                console.error('播放失败:', error);
                updateStatus('播放失败: ' + error.message, 'error');
            }
        });

        // 暂停按钮
        pauseBtn.addEventListener('click', () => {
            audio.pause();
            updateStatus('已暂停', 'paused');
        });

        // 停止按钮
        stopBtn.addEventListener('click', () => {
            audio.pause();
            audio.currentTime = 0;
            updateStatus('已停止', 'paused');
        });

        // 音量控制
        volumeSlider.addEventListener('input', (e) => {
            const volume = parseFloat(e.target.value);
            audio.volume = volume;
            volumeValue.textContent = Math.round(volume * 100) + '%';
        });

        // 音频事件监听
        audio.addEventListener('loadstart', () => {
            updateStatus('开始加载音频...', '');
        });

        audio.addEventListener('canplay', () => {
            updateStatus('音频已准备就绪', '');
        });

        audio.addEventListener('canplaythrough', () => {
            updateStatus('音频完全加载完成', '');
        });

        audio.addEventListener('error', (e) => {
            updateStatus('音频加载错误', 'error');
            console.error('Audio error:', e);
        });

        audio.addEventListener('play', () => {
            updateStatus('正在播放...', 'playing');
        });

        audio.addEventListener('pause', () => {
            updateStatus('已暂停', 'paused');
        });

        audio.addEventListener('timeupdate', () => {
            currentTime.textContent = formatTime(audio.currentTime);
        });

        audio.addEventListener('loadedmetadata', () => {
            duration.textContent = formatTime(audio.duration);
        });

        // 初始化
        audio.volume = 0.3;
        updateStatus('准备就绪 - 点击播放按钮测试音乐', '');
    </script>
</body>
</html>
