<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音乐调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .debug-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-btn {
            background: #ff6b9d;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-btn:hover {
            background: #e55a87;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🎵 音乐功能调试</h1>
        
        <div class="status" id="status">准备测试...</div>
        
        <div>
            <button class="test-btn" onclick="testAudioExists()">1. 检查音频元素</button>
            <button class="test-btn" onclick="testAudioFile()">2. 检查音频文件</button>
            <button class="test-btn" onclick="testPlayAudio()">3. 测试播放</button>
            <button class="test-btn" onclick="testVueApp()">4. 测试Vue应用</button>
        </div>
        
        <div>
            <h3>音频控制：</h3>
            <button class="test-btn" onclick="playAudio()">▶️ 播放</button>
            <button class="test-btn" onclick="pauseAudio()">⏸️ 暂停</button>
            <button class="test-btn" onclick="checkVolume()">🔊 检查音量</button>
        </div>
        
        <div>
            <h3>文件信息：</h3>
            <p><strong>音频文件路径：</strong>assets/audio/romantic-musi.mp3</p>
            <p><strong>当前页面：</strong><span id="currentUrl"></span></p>
            <p><strong>音频状态：</strong><span id="audioStatus">未知</span></p>
        </div>
        
        <div>
            <a href="index.html">← 返回主应用</a> | 
            <a href="music-test.html">音乐测试页面</a>
        </div>
    </div>

    <!-- 音频元素 -->
    <audio id="debugAudio" loop>
        <source src="assets/audio/romantic-musi.mp3" type="audio/mpeg">
        <source src="assets/audio/romantic-musi.mp3" type="audio/mp3">
    </audio>

    <script>
        const status = document.getElementById('status');
        const audioStatus = document.getElementById('audioStatus');
        const currentUrl = document.getElementById('currentUrl');
        const audio = document.getElementById('debugAudio');
        
        // 显示当前URL
        currentUrl.textContent = window.location.href;
        
        function updateStatus(message, type = 'info') {
            status.textContent = message;
            status.className = 'status ' + type;
            console.log('[DEBUG]', message);
        }
        
        function updateAudioStatus() {
            if (audio.paused) {
                audioStatus.textContent = '暂停';
            } else {
                audioStatus.textContent = '播放中';
            }
        }
        
        // 测试1：检查音频元素
        function testAudioExists() {
            if (audio) {
                updateStatus('✅ 音频元素存在', 'success');
                updateStatus(`音频源数量: ${audio.children.length}`, 'info');
            } else {
                updateStatus('❌ 音频元素不存在', 'error');
            }
        }
        
        // 测试2：检查音频文件
        function testAudioFile() {
            updateStatus('正在检查音频文件...', 'info');
            
            fetch('assets/audio/romantic-musi.mp3')
                .then(response => {
                    if (response.ok) {
                        updateStatus(`✅ 音频文件存在 (${response.status})`, 'success');
                        return response.blob();
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                })
                .then(blob => {
                    updateStatus(`✅ 音频文件大小: ${(blob.size / 1024 / 1024).toFixed(2)} MB`, 'success');
                })
                .catch(error => {
                    updateStatus(`❌ 音频文件加载失败: ${error.message}`, 'error');
                });
        }
        
        // 测试3：测试播放
        async function testPlayAudio() {
            try {
                updateStatus('正在尝试播放音频...', 'info');
                audio.volume = 0.3;
                await audio.play();
                updateStatus('✅ 音频播放成功', 'success');
                updateAudioStatus();
            } catch (error) {
                updateStatus(`❌ 音频播放失败: ${error.message}`, 'error');
                console.error('Play error:', error);
            }
        }
        
        // 测试4：测试Vue应用
        function testVueApp() {
            if (typeof Vue !== 'undefined') {
                updateStatus('✅ Vue.js 已加载', 'success');
            } else {
                updateStatus('❌ Vue.js 未加载', 'error');
            }
            
            // 检查主应用的音频元素
            const mainAudio = document.getElementById('bg-music');
            if (mainAudio) {
                updateStatus('✅ 主应用音频元素存在', 'success');
            } else {
                updateStatus('❌ 主应用音频元素不存在', 'error');
            }
        }
        
        // 播放音频
        async function playAudio() {
            try {
                await audio.play();
                updateStatus('音频开始播放', 'success');
                updateAudioStatus();
            } catch (error) {
                updateStatus(`播放失败: ${error.message}`, 'error');
            }
        }
        
        // 暂停音频
        function pauseAudio() {
            audio.pause();
            updateStatus('音频已暂停', 'info');
            updateAudioStatus();
        }
        
        // 检查音量
        function checkVolume() {
            updateStatus(`当前音量: ${Math.round(audio.volume * 100)}%`, 'info');
        }
        
        // 音频事件监听
        audio.addEventListener('loadstart', () => {
            updateStatus('开始加载音频...', 'info');
        });
        
        audio.addEventListener('canplay', () => {
            updateStatus('音频可以播放', 'success');
        });
        
        audio.addEventListener('error', (e) => {
            updateStatus('音频加载错误', 'error');
            console.error('Audio error:', e);
        });
        
        audio.addEventListener('play', () => {
            updateAudioStatus();
        });
        
        audio.addEventListener('pause', () => {
            updateAudioStatus();
        });
        
        // 初始化
        updateAudioStatus();
        updateStatus('调试页面已加载，点击按钮开始测试', 'info');
    </script>
</body>
</html>
