<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="心动告白 - 一个浪漫的告白应用">
    <meta name="theme-color" content="#ff6b9d">
    <title>心动告白 - Heart Confession</title>

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- Favicon -->
    <link rel="apple-touch-icon" href="assets/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="192x192" href="assets/icon-192x192.png">

    <!-- CSS -->
    <link rel="stylesheet" href="css/style.css">

    <!-- Vue.js 3 CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>

    <!-- GSAP Animation Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/TextPlugin.min.js"></script>
</head>
<body>
    <div id="app">
        <div class="app-container">
            <!-- Background particles -->
            <div id="particles-container"></div>

            <!-- Header -->
            <header class="header">
                <h1 class="app-title">心动告白</h1>
                <div class="progress-indicator">
                    <span>{{ currentMessage }}/6</span>
                </div>
            </header>

            <!-- Main content -->
            <main class="main-content">
                <!-- Heart container -->
                <div class="heart-container">
                    <canvas
                        ref="heartCanvas"
                        id="heart-canvas"
                        width="300"
                        height="300"
                        @click="handleHeartClick"
                        :class="{ 'heart-explode': isAnimating }"
                    ></canvas>
                    <div class="heart-prompt">
                        <span>{{ heartPromptText }}</span>
                    </div>
                </div>

                <!-- Message display -->
                <div class="message-container">
                    <div class="message-box">
                        <p class="message-text" :key="messageKey">{{ currentMessageText }}</p>
                    </div>
                </div>

                <!-- Action buttons -->
                <div class="action-buttons">
                    <button @click="toggleMusic" class="action-btn" :class="{ 'music-active': isMusicPlaying }">
                        <span>{{ isMusicPlaying ? '🎵' : '🔇' }}</span>
                        <span>音乐</span>
                    </button>
                    <button @click="shareApp" class="action-btn">
                        <span>📱</span>
                        <span>分享</span>
                    </button>
                    <button @click="installApp" class="action-btn" v-show="canInstall">
                        <span>📲</span>
                        <span>安装</span>
                    </button>
                    <button @click="takeScreenshot" class="action-btn">
                        <span>📸</span>
                        <span>截图</span>
                    </button>
                </div>
            </main>

            <!-- Settings modal -->
            <div class="modal" :class="{ active: showSettingsModal }" @click="closeModalOnBackdrop">
                <div class="modal-content">
                    <h2>自定义告白</h2>
                    <div class="custom-messages">
                        <div class="message-input-group" v-for="(message, index) in customMessages" :key="index">
                            <label :for="`message-${index + 1}`">第{{ index + 1 }}句：</label>
                            <input
                                type="text"
                                :id="`message-${index + 1}`"
                                v-model="customMessages[index]"
                                :placeholder="getPlaceholder(index)"
                            >
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button @click="saveCustomMessages" class="btn-primary">保存</button>
                        <button @click="closeSettings" class="btn-secondary">取消</button>
                        <button @click="resetToDefault" class="btn-secondary">恢复默认</button>
                    </div>
                </div>
            </div>

            <!-- Settings button -->
            <button @click="openSettings" class="settings-btn" title="自定义告白">⚙️</button>

            <!-- Toast notification -->
            <div v-if="toast.show" class="toast" :class="toast.type">
                {{ toast.message }}
            </div>
        </div>
    </div>

    <!-- Background audio -->
    <audio ref="bgMusic" id="bg-music" loop preload="none">
        <!-- 暂时禁用音频，避免加载错误 -->
        <!-- <source src="assets/audio/romantic-music.mp3" type="audio/mpeg"> -->
    </audio>

    <!-- Vue.js 3 Application -->
    <script type="module" src="js/vue-app.js"></script>
</body>
</html>