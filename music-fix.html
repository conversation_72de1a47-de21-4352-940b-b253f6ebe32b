<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音乐功能修复版</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #ff6b9d 0%, #c44569 50%, #f8b500 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0;
        }
        .container {
            background: rgba(255, 255, 255, 0.9);
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            max-width: 500px;
        }
        .music-btn {
            background: #ff6b9d;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.2rem;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .music-btn:hover {
            background: #e55a87;
            transform: translateY(-2px);
        }
        .music-btn.playing {
            background: #28a745;
            animation: pulse 1s infinite;
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            font-weight: bold;
            font-size: 1.1rem;
        }
        .status.playing {
            background: rgba(76, 175, 80, 0.2);
            color: #2e7d32;
        }
        .status.paused {
            background: rgba(255, 152, 0, 0.2);
            color: #f57c00;
        }
        .status.error {
            background: rgba(244, 67, 54, 0.2);
            color: #c62828;
        }
        .heart {
            font-size: 4rem;
            margin: 20px 0;
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        .heart:hover {
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <h1>🎵 音乐功能修复版</h1>
            
            <div class="heart" @click="clickHeart">❤️</div>
            
            <div class="status" :class="statusClass">
                {{ statusMessage }}
            </div>
            
            <div>
                <button 
                    class="music-btn" 
                    :class="{ playing: isMusicPlaying }"
                    @click="toggleMusic"
                >
                    {{ isMusicPlaying ? '⏸️ 暂停音乐' : '▶️ 播放音乐' }}
                </button>
            </div>
            
            <div>
                <button class="music-btn" @click="testAudio">🔧 测试音频文件</button>
                <button class="music-btn" @click="checkAudioElement">📋 检查音频元素</button>
            </div>
            
            <div style="margin-top: 20px;">
                <p><strong>音频文件：</strong>assets/audio/romantic-musi.mp3</p>
                <p><strong>音量：</strong>{{ Math.round(currentVolume * 100) }}%</p>
                <p><strong>当前时间：</strong>{{ formatTime(currentTime) }} / {{ formatTime(duration) }}</p>
            </div>
            
            <div style="margin-top: 20px;">
                <a href="index.html">← 返回主应用</a>
            </div>
        </div>
    </div>

    <!-- 音频元素 -->
    <audio ref="audio" id="music-audio" loop preload="metadata">
        <source src="assets/audio/romantic-musi.mp3" type="audio/mpeg">
        <source src="assets/audio/romantic-musi.mp3" type="audio/mp3">
        您的浏览器不支持音频播放。
    </audio>

    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script>
        const { createApp, ref, computed, onMounted } = Vue;

        createApp({
            setup() {
                const isMusicPlaying = ref(false);
                const statusMessage = ref('准备就绪');
                const statusClass = ref('');
                const currentVolume = ref(0.3);
                const currentTime = ref(0);
                const duration = ref(0);
                const audio = ref(null);

                const formatTime = (seconds) => {
                    const mins = Math.floor(seconds / 60);
                    const secs = Math.floor(seconds % 60);
                    return `${mins}:${secs.toString().padStart(2, '0')}`;
                };

                const updateStatus = (message, type = '') => {
                    statusMessage.value = message;
                    statusClass.value = type;
                    console.log('[MUSIC]', message);
                };

                const toggleMusic = async () => {
                    const audioElement = audio.value;
                    console.log('Toggle music clicked, audio element:', audioElement);
                    
                    if (!audioElement) {
                        updateStatus('❌ 音频元素不存在', 'error');
                        return;
                    }

                    try {
                        if (isMusicPlaying.value) {
                            audioElement.pause();
                            isMusicPlaying.value = false;
                            updateStatus('⏸️ 音乐已暂停', 'paused');
                        } else {
                            audioElement.volume = currentVolume.value;
                            await audioElement.play();
                            isMusicPlaying.value = true;
                            updateStatus('▶️ 音乐正在播放', 'playing');
                        }
                    } catch (error) {
                        console.error('Music toggle error:', error);
                        updateStatus(`❌ 播放失败: ${error.message}`, 'error');
                    }
                };

                const clickHeart = async () => {
                    updateStatus('💖 心形被点击了！', 'playing');
                    
                    // 尝试自动播放音乐
                    if (!isMusicPlaying.value) {
                        await toggleMusic();
                    }
                };

                const testAudio = async () => {
                    updateStatus('🔍 正在测试音频文件...', '');
                    
                    try {
                        const response = await fetch('assets/audio/romantic-musi.mp3');
                        if (response.ok) {
                            const blob = await response.blob();
                            const sizeMB = (blob.size / 1024 / 1024).toFixed(2);
                            updateStatus(`✅ 音频文件正常 (${sizeMB} MB)`, 'playing');
                        } else {
                            updateStatus(`❌ 音频文件不存在 (${response.status})`, 'error');
                        }
                    } catch (error) {
                        updateStatus(`❌ 音频文件测试失败: ${error.message}`, 'error');
                    }
                };

                const checkAudioElement = () => {
                    const audioElement = audio.value;
                    console.log('Audio element check:', audioElement);
                    
                    if (audioElement) {
                        updateStatus(`✅ 音频元素正常 (${audioElement.children.length} 个源)`, 'playing');
                        console.log('Audio sources:', Array.from(audioElement.children).map(child => child.src));
                    } else {
                        updateStatus('❌ 音频元素不存在', 'error');
                    }
                };

                onMounted(() => {
                    const audioElement = audio.value;
                    if (audioElement) {
                        // 音频事件监听
                        audioElement.addEventListener('loadstart', () => {
                            updateStatus('📥 开始加载音频...', '');
                        });

                        audioElement.addEventListener('canplay', () => {
                            updateStatus('✅ 音频可以播放', 'playing');
                        });

                        audioElement.addEventListener('error', (e) => {
                            updateStatus('❌ 音频加载错误', 'error');
                            console.error('Audio error:', e);
                        });

                        audioElement.addEventListener('play', () => {
                            isMusicPlaying.value = true;
                            updateStatus('▶️ 音乐正在播放', 'playing');
                        });

                        audioElement.addEventListener('pause', () => {
                            isMusicPlaying.value = false;
                            updateStatus('⏸️ 音乐已暂停', 'paused');
                        });

                        audioElement.addEventListener('timeupdate', () => {
                            currentTime.value = audioElement.currentTime;
                        });

                        audioElement.addEventListener('loadedmetadata', () => {
                            duration.value = audioElement.duration;
                        });

                        // 设置初始音量
                        audioElement.volume = currentVolume.value;
                        
                        updateStatus('🎵 音乐功能已初始化', 'playing');
                    } else {
                        updateStatus('❌ 音频元素初始化失败', 'error');
                    }
                });

                return {
                    isMusicPlaying,
                    statusMessage,
                    statusClass,
                    currentVolume,
                    currentTime,
                    duration,
                    audio,
                    toggleMusic,
                    clickHeart,
                    testAudio,
                    checkAudioElement,
                    formatTime
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
