<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="心动告白 - 一个浪漫的告白应用">
    <meta name="theme-color" content="#ff6b9d">
    <title>心动告白 - Heart Confession (原生JS版)</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <h1 class="app-title">心动告白</h1>
            <div class="progress-indicator">
                <span id="progress-text">0/6</span>
            </div>
        </header>
        
        <!-- Main content -->
        <main class="main-content">
            <!-- Heart container -->
            <div class="heart-container">
                <canvas 
                    id="heart-canvas" 
                    width="300" 
                    height="300"
                ></canvas>
                <div class="heart-prompt">
                    <span id="heart-prompt-text">点击❤️开始告白</span>
                </div>
            </div>
            
            <!-- Message display -->
            <div class="message-container">
                <div class="message-box">
                    <p class="message-text" id="message-text">准备好接受我的告白了吗？</p>
                </div>
            </div>
            
            <!-- Action buttons -->
            <div class="action-buttons">
                <button id="music-btn" class="action-btn">
                    <span id="music-icon">🔇</span>
                    <span id="music-text">音乐</span>
                </button>
                <button id="share-btn" class="action-btn">
                    <span>📱</span>
                    <span>分享</span>
                </button>
                <button id="screenshot-btn" class="action-btn">
                    <span>📸</span>
                    <span>截图</span>
                </button>
            </div>
        </main>
        
        <!-- Toast notification -->
        <div id="toast" class="toast" style="display: none;">
            <span id="toast-message"></span>
        </div>
        
        <!-- Debug panel -->
        <div style="position: fixed; top: 10px; right: 10px; background: rgba(0,0,0,0.8); color: white; padding: 10px; border-radius: 5px; font-size: 12px; z-index: 9999;">
            <div>🎵 状态: <span id="music-status">已暂停</span></div>
            <div>版本: 原生JS v1.0</div>
            <button id="debug-btn" style="margin-top: 5px; padding: 2px 8px; font-size: 10px;">调试音频</button>
        </div>
    </div>
    
    <!-- Background audio -->
    <audio id="bg-music" loop preload="metadata">
        <source src="assets/audio/romantic-musi.mp3" type="audio/mpeg">
        <source src="assets/audio/romantic-musi.mp3" type="audio/mp3">
        您的浏览器不支持音频播放。
    </audio>

    <script>
        // 应用状态
        let currentMessage = 0;
        let isAnimating = false;
        let isMusicPlaying = false;
        let heartRenderer = null;
        
        // 告白消息
        const messages = [
            "你知道吗？你的笑容是我见过最美的风景。",
            "每当我看到你，我的心跳都会不由自主地加速。",
            "和你在一起的每一刻，都让我感到无比幸福。",
            "你的温柔如春风，轻抚着我的心田。",
            "我想牵着你的手，走过人生的每一个春夏秋冬。",
            "我爱你，不只是今天，而是每一个明天。"
        ];
        
        // DOM元素
        const heartCanvas = document.getElementById('heart-canvas');
        const bgMusic = document.getElementById('bg-music');
        const musicBtn = document.getElementById('music-btn');
        const musicIcon = document.getElementById('music-icon');
        const musicText = document.getElementById('music-text');
        const musicStatus = document.getElementById('music-status');
        const messageText = document.getElementById('message-text');
        const progressText = document.getElementById('progress-text');
        const heartPromptText = document.getElementById('heart-prompt-text');
        const shareBtn = document.getElementById('share-btn');
        const screenshotBtn = document.getElementById('screenshot-btn');
        const debugBtn = document.getElementById('debug-btn');
        const toast = document.getElementById('toast');
        const toastMessage = document.getElementById('toast-message');

        // 简化的心形渲染器
        class SimpleHeartRenderer {
            constructor(canvas) {
                this.canvas = canvas;
                this.ctx = canvas.getContext('2d');
                this.scale = 1;
                this.init();
            }
            
            init() {
                this.setupCanvas();
                this.draw();
            }
            
            setupCanvas() {
                const size = Math.min(window.innerWidth - 40, 300);
                this.canvas.width = size;
                this.canvas.height = size;
                this.canvas.style.width = size + 'px';
                this.canvas.style.height = size + 'px';
            }
            
            draw() {
                const ctx = this.ctx;
                const width = this.canvas.width;
                const height = this.canvas.height;
                
                ctx.clearRect(0, 0, width, height);
                ctx.save();
                ctx.translate(width / 2, height / 2);
                ctx.scale(this.scale, this.scale);
                
                ctx.beginPath();
                ctx.fillStyle = '#ff6b9d';
                
                const size = Math.min(width, height) * 0.3;
                for (let t = 0; t <= Math.PI * 2; t += 0.01) {
                    const x = size * (16 * Math.pow(Math.sin(t), 3));
                    const y = -size * (13 * Math.cos(t) - 5 * Math.cos(2 * t) - 2 * Math.cos(3 * t) - Math.cos(4 * t));
                    
                    if (t === 0) {
                        ctx.moveTo(x / 16, y / 16);
                    } else {
                        ctx.lineTo(x / 16, y / 16);
                    }
                }
                
                ctx.closePath();
                ctx.fill();
                ctx.shadowColor = 'rgba(255, 107, 157, 0.5)';
                ctx.shadowBlur = 20;
                ctx.fill();
                ctx.restore();
            }
            
            animateClick() {
                this.scale = 1.2;
                this.draw();
                setTimeout(() => {
                    this.scale = 1;
                    this.draw();
                }, 300);
            }
        }

        // Toast通知
        function showToast(message, type = 'success') {
            toastMessage.textContent = message;
            toast.className = `toast ${type}`;
            toast.style.display = 'block';
            
            setTimeout(() => {
                toast.style.display = 'none';
            }, 2000);
        }

        // 更新UI状态
        function updateUI() {
            // 更新进度
            progressText.textContent = `${currentMessage}/6`;
            
            // 更新消息
            messageText.textContent = messages[currentMessage] || '准备好接受我的告白了吗？';
            
            // 更新提示文字
            if (currentMessage === 0) {
                heartPromptText.textContent = '点击❤️开始告白';
            } else {
                const remaining = 6 - currentMessage;
                heartPromptText.textContent = remaining > 0 ? `继续点击❤️ (${remaining}次)` : '点击❤️重新开始';
            }
            
            // 更新音乐状态
            musicIcon.textContent = isMusicPlaying ? '🎵' : '🔇';
            musicText.textContent = isMusicPlaying ? '播放中' : '音乐';
            musicStatus.textContent = isMusicPlaying ? '播放中' : '已暂停';
            
            if (isMusicPlaying) {
                musicBtn.classList.add('music-active');
            } else {
                musicBtn.classList.remove('music-active');
            }
        }

        // 心形点击处理
        function handleHeartClick() {
            if (isAnimating) return;
            
            console.log('Heart clicked, current message:', currentMessage);
            
            // 第一次点击时尝试播放音乐
            if (currentMessage === 0 && !isMusicPlaying) {
                toggleMusic();
            }
            
            isAnimating = true;
            
            // 心形动画
            if (heartRenderer) {
                heartRenderer.animateClick();
            }
            
            // 更新消息
            currentMessage = (currentMessage + 1) % messages.length;
            
            // 更新UI
            updateUI();
            
            setTimeout(() => {
                isAnimating = false;
            }, 600);
        }

        // 音乐控制
        async function toggleMusic() {
            console.log('Toggle music called, current state:', isMusicPlaying);
            
            if (!bgMusic) {
                console.error('Audio element not found');
                showToast('音频元素未找到', 'error');
                return;
            }
            
            try {
                if (isMusicPlaying) {
                    console.log('Pausing music');
                    bgMusic.pause();
                    isMusicPlaying = false;
                    showToast('音乐已暂停', 'success');
                } else {
                    console.log('Playing music');
                    bgMusic.volume = 0.3;
                    await bgMusic.play();
                    isMusicPlaying = true;
                    showToast('音乐已开启', 'success');
                }
                updateUI();
            } catch (error) {
                console.error('Music toggle error:', error);
                showToast(`播放失败: ${error.message}`, 'error');
            }
        }

        // 分享功能
        async function shareApp() {
            try {
                if (navigator.share) {
                    await navigator.share({
                        title: '心动告白',
                        text: '用这个浪漫的告白应用表达你的心意吧！',
                        url: window.location.href
                    });
                } else {
                    await navigator.clipboard.writeText(window.location.href);
                    showToast('链接已复制到剪贴板', 'success');
                }
            } catch (error) {
                showToast('分享失败', 'error');
            }
        }

        // 截图功能
        function takeScreenshot() {
            const flash = document.createElement('div');
            flash.className = 'screenshot-flash';
            document.body.appendChild(flash);
            
            setTimeout(() => {
                document.body.removeChild(flash);
            }, 300);
            
            showToast('请使用浏览器的截图功能', 'warning');
        }

        // 调试音频
        function debugAudio() {
            console.log('🔧 Audio debug info:');
            console.log('- Element:', bgMusic);
            console.log('- Src:', bgMusic?.src);
            console.log('- Paused:', bgMusic?.paused);
            console.log('- Volume:', bgMusic?.volume);
            console.log('- ReadyState:', bgMusic?.readyState);
            console.log('- NetworkState:', bgMusic?.networkState);
            
            showToast(`音频调试: ${bgMusic ? '元素存在' : '元素不存在'}`, bgMusic ? 'success' : 'error');
        }

        // 初始化应用
        function initApp() {
            console.log('Initializing Heart Confession App (Native JS)');
            
            // 初始化心形渲染器
            if (heartCanvas) {
                heartRenderer = new SimpleHeartRenderer(heartCanvas);
                heartCanvas.addEventListener('click', handleHeartClick);
                console.log('Heart renderer initialized');
            }
            
            // 初始化音频
            if (bgMusic) {
                bgMusic.volume = 0.3;
                
                bgMusic.addEventListener('play', () => {
                    console.log('Audio play event');
                    isMusicPlaying = true;
                    updateUI();
                });
                
                bgMusic.addEventListener('pause', () => {
                    console.log('Audio pause event');
                    isMusicPlaying = false;
                    updateUI();
                });
                
                bgMusic.addEventListener('error', (e) => {
                    console.error('Audio error:', e);
                    showToast('音频加载失败', 'error');
                });
                
                console.log('Audio initialized');
            }
            
            // 绑定事件
            if (musicBtn) musicBtn.addEventListener('click', toggleMusic);
            if (shareBtn) shareBtn.addEventListener('click', shareApp);
            if (screenshotBtn) screenshotBtn.addEventListener('click', takeScreenshot);
            if (debugBtn) debugBtn.addEventListener('click', debugAudio);
            
            // 初始化UI
            updateUI();
            
            console.log('✅ App initialized successfully');
            showToast('应用已加载，点击心形开始告白！', 'success');
        }

        // 页面加载完成后初始化
        window.addEventListener('load', initApp);
        
        console.log('🚀 Heart Confession App (Native JS) script loaded');
    </script>
</body>
</html>
