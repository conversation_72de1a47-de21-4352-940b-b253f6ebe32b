<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="心动告白 - 一个浪漫的告白应用">
    <meta name="theme-color" content="#ff6b9d">
    <title>心动告白 - Heart Confession (原生JS版)</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <h1 class="app-title">心动告白</h1>
            <div class="progress-indicator">
                <span id="progress-text">0/6</span>
            </div>
        </header>
        
        <!-- Main content -->
        <main class="main-content">
            <!-- Heart container -->
            <div class="heart-container">
                <canvas 
                    id="heart-canvas" 
                    width="300" 
                    height="300"
                ></canvas>
                <div class="heart-prompt">
                    <span id="heart-prompt-text">点击❤️开始告白</span>
                </div>
            </div>
            
            <!-- Message display -->
            <div class="message-container">
                <div class="message-box">
                    <p class="message-text" id="message-text">准备好接受我的告白了吗？</p>
                </div>
            </div>
            
            <!-- Action buttons -->
            <div class="action-buttons">
                <button id="music-btn" class="action-btn">
                    <span id="music-icon">🔇</span>
                    <span id="music-text">音乐</span>
                </button>
                <button id="share-btn" class="action-btn">
                    <span>📱</span>
                    <span>分享</span>
                </button>
                <button id="screenshot-btn" class="action-btn">
                    <span>📸</span>
                    <span>截图</span>
                </button>
                <button id="settings-btn" class="action-btn">
                    <span>⚙️</span>
                    <span>设置</span>
                </button>
            </div>
        </main>
        
        <!-- Settings modal -->
        <div id="settings-modal" class="modal" style="display: none;">
            <div class="modal-content">
                <h2>应用设置</h2>

                <!-- Tab navigation -->
                <div class="tab-navigation">
                    <button class="tab-btn active" data-tab="messages">💝 告白设置</button>
                    <button class="tab-btn" data-tab="music">🎵 音乐设置</button>
                </div>

                <!-- Messages tab -->
                <div id="messages-tab" class="tab-content active">
                    <h3>自定义告白语句</h3>
                    <div class="custom-messages">
                        <div class="message-input-group">
                            <label for="message-1">第1句：</label>
                            <input type="text" id="message-1" placeholder="友好的赞美...">
                        </div>
                        <div class="message-input-group">
                            <label for="message-2">第2句：</label>
                            <input type="text" id="message-2" placeholder="温暖的关心...">
                        </div>
                        <div class="message-input-group">
                            <label for="message-3">第3句：</label>
                            <input type="text" id="message-3" placeholder="真诚的欣赏...">
                        </div>
                        <div class="message-input-group">
                            <label for="message-4">第4句：</label>
                            <input type="text" id="message-4" placeholder="深情的表达...">
                        </div>
                        <div class="message-input-group">
                            <label for="message-5">第5句：</label>
                            <input type="text" id="message-5" placeholder="浪漫的承诺...">
                        </div>
                        <div class="message-input-group">
                            <label for="message-6">第6句：</label>
                            <input type="text" id="message-6" placeholder="深情的告白...">
                        </div>
                    </div>
                </div>

                <!-- Music tab -->
                <div id="music-tab" class="tab-content">
                    <h3>选择背景音乐</h3>

                    <!-- Music upload area -->
                    <div class="music-upload-area">
                        <div class="upload-zone" id="upload-zone">
                            <div class="upload-icon">📁</div>
                            <div class="upload-text">
                                <p><strong>点击或拖拽上传音乐文件</strong></p>
                                <p>支持 MP3, WAV, OGG 格式，最大 10MB</p>
                            </div>
                            <input type="file" id="music-file-input" accept="audio/*" style="display: none;">
                        </div>
                    </div>

                    <!-- Music list -->
                    <div class="music-list">
                        <h4>可用音乐</h4>
                        <div id="music-items" class="music-items">
                            <!-- Music items will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <div class="modal-actions">
                    <button id="save-settings-btn" class="btn-primary">保存设置</button>
                    <button id="cancel-settings-btn" class="btn-secondary">取消</button>
                    <button id="reset-settings-btn" class="btn-secondary">恢复默认</button>
                </div>
            </div>
        </div>

        <!-- Toast notification -->
        <div id="toast" class="toast" style="display: none;">
            <span id="toast-message"></span>
        </div>
        
        <!-- Debug panel -->
        <div style="position: fixed; top: 10px; right: 10px; background: rgba(0,0,0,0.8); color: white; padding: 10px; border-radius: 5px; font-size: 12px; z-index: 9999;">
            <div>🎵 状态: <span id="music-status">已暂停</span></div>
            <div>版本: 原生JS v1.0</div>
            <button id="debug-btn" style="margin-top: 5px; padding: 2px 8px; font-size: 10px;">调试音频</button>
        </div>
    </div>
    
    <!-- Background audio -->
    <audio id="bg-music" loop preload="metadata">
        <source src="assets/audio/romantic-musi.mp3" type="audio/mpeg">
        <source src="assets/audio/romantic-musi.mp3" type="audio/mp3">
        您的浏览器不支持音频播放。
    </audio>

    <script>
        // 应用状态
        let currentMessage = 0;
        let isAnimating = false;
        let isMusicPlaying = false;
        let heartRenderer = null;
        let customMessages = [];
        let showSettingsModal = false;
        let currentTab = 'messages';
        let musicManager = null;
        let previewAudio = null;
        
        // 告白消息
        const messages = [
            "你知道吗？你的笑容是我见过最美的风景。",
            "每当我看到你，我的心跳都会不由自主地加速。",
            "和你在一起的每一刻，都让我感到无比幸福。",
            "你的温柔如春风，轻抚着我的心田。",
            "我想牵着你的手，走过人生的每一个春夏秋冬。",
            "我爱你，不只是今天，而是每一个明天。"
        ];
        
        // DOM元素
        const heartCanvas = document.getElementById('heart-canvas');
        const bgMusic = document.getElementById('bg-music');
        const musicBtn = document.getElementById('music-btn');
        const musicIcon = document.getElementById('music-icon');
        const musicText = document.getElementById('music-text');
        const musicStatus = document.getElementById('music-status');
        const messageText = document.getElementById('message-text');
        const progressText = document.getElementById('progress-text');
        const heartPromptText = document.getElementById('heart-prompt-text');
        const shareBtn = document.getElementById('share-btn');
        const screenshotBtn = document.getElementById('screenshot-btn');
        const settingsBtn = document.getElementById('settings-btn');
        const debugBtn = document.getElementById('debug-btn');
        const toast = document.getElementById('toast');
        const toastMessage = document.getElementById('toast-message');
        const settingsModal = document.getElementById('settings-modal');
        const saveSettingsBtn = document.getElementById('save-settings-btn');
        const cancelSettingsBtn = document.getElementById('cancel-settings-btn');
        const resetSettingsBtn = document.getElementById('reset-settings-btn');
        const uploadZone = document.getElementById('upload-zone');
        const musicFileInput = document.getElementById('music-file-input');
        const musicItems = document.getElementById('music-items');

        // 音乐管理器
        class MusicManager {
            constructor() {
                this.musicList = [];
                this.currentMusic = null;
                this.selectedMusic = null;
                this.loadDefaultMusic();
                this.loadUserMusic();
            }

            loadDefaultMusic() {
                // 默认音乐列表
                const defaultMusic = [
                    {
                        id: 'default-1',
                        name: '浪漫旋律',
                        url: 'assets/audio/romantic-musi.mp3',
                        type: 'default',
                        duration: '未知',
                        description: '温柔浪漫的背景音乐'
                    },
                    {
                        id: 'default-2',
                        name: '甜蜜时光',
                        url: 'assets/audio/default/romantic-melody.mp3',
                        type: 'default',
                        duration: '未知',
                        description: '甜蜜温馨的告白音乐'
                    }
                ];

                this.musicList = [...defaultMusic];
                this.selectedMusic = this.musicList[0];
            }

            loadUserMusic() {
                try {
                    const saved = localStorage.getItem('heartapp_user_music');
                    if (saved) {
                        const userMusic = JSON.parse(saved);
                        this.musicList.push(...userMusic);
                    }

                    const selectedId = localStorage.getItem('heartapp_selected_music');
                    if (selectedId) {
                        const selected = this.musicList.find(m => m.id === selectedId);
                        if (selected) {
                            this.selectedMusic = selected;
                        }
                    }
                } catch (error) {
                    console.error('Failed to load user music:', error);
                }
            }

            saveUserMusic() {
                try {
                    const userMusic = this.musicList.filter(m => m.type === 'user');
                    localStorage.setItem('heartapp_user_music', JSON.stringify(userMusic));

                    if (this.selectedMusic) {
                        localStorage.setItem('heartapp_selected_music', this.selectedMusic.id);
                    }
                } catch (error) {
                    console.error('Failed to save user music:', error);
                }
            }

            addMusic(file) {
                return new Promise((resolve, reject) => {
                    // 验证文件类型
                    if (!file.type.startsWith('audio/')) {
                        reject(new Error('请选择音频文件'));
                        return;
                    }

                    // 验证文件大小 (10MB)
                    if (file.size > 10 * 1024 * 1024) {
                        reject(new Error('文件大小不能超过10MB'));
                        return;
                    }

                    const reader = new FileReader();
                    reader.onload = (e) => {
                        const music = {
                            id: 'user-' + Date.now(),
                            name: file.name.replace(/\.[^/.]+$/, ''),
                            url: e.target.result,
                            type: 'user',
                            duration: '未知',
                            file: file
                        };

                        this.musicList.push(music);
                        this.saveUserMusic();
                        resolve(music);
                    };

                    reader.onerror = () => {
                        reject(new Error('文件读取失败'));
                    };

                    reader.readAsDataURL(file);
                });
            }

            removeMusic(musicId) {
                const index = this.musicList.findIndex(m => m.id === musicId);
                if (index > -1) {
                    const music = this.musicList[index];
                    if (music.type === 'user') {
                        this.musicList.splice(index, 1);

                        // 如果删除的是当前选中的音乐，切换到默认音乐
                        if (this.selectedMusic && this.selectedMusic.id === musicId) {
                            this.selectedMusic = this.musicList.find(m => m.type === 'default');
                        }

                        this.saveUserMusic();
                        return true;
                    }
                }
                return false;
            }

            selectMusic(musicId) {
                const music = this.musicList.find(m => m.id === musicId);
                if (music) {
                    this.selectedMusic = music;
                    this.saveUserMusic();
                    return true;
                }
                return false;
            }

            getCurrentMusicUrl() {
                return this.selectedMusic ? this.selectedMusic.url : null;
            }

            getMusicList() {
                return this.musicList;
            }

            getSelectedMusic() {
                return this.selectedMusic;
            }
        }

        // 简化的心形渲染器
        class SimpleHeartRenderer {
            constructor(canvas) {
                this.canvas = canvas;
                this.ctx = canvas.getContext('2d');
                this.scale = 1;
                this.init();
            }
            
            init() {
                this.setupCanvas();
                this.draw();
            }
            
            setupCanvas() {
                const size = Math.min(window.innerWidth - 40, 300);
                this.canvas.width = size;
                this.canvas.height = size;
                this.canvas.style.width = size + 'px';
                this.canvas.style.height = size + 'px';
            }
            
            draw() {
                const ctx = this.ctx;
                const width = this.canvas.width;
                const height = this.canvas.height;
                
                ctx.clearRect(0, 0, width, height);
                ctx.save();
                ctx.translate(width / 2, height / 2);
                ctx.scale(this.scale, this.scale);
                
                ctx.beginPath();
                ctx.fillStyle = '#ff6b9d';
                
                const size = Math.min(width, height) * 0.3;
                for (let t = 0; t <= Math.PI * 2; t += 0.01) {
                    const x = size * (16 * Math.pow(Math.sin(t), 3));
                    const y = -size * (13 * Math.cos(t) - 5 * Math.cos(2 * t) - 2 * Math.cos(3 * t) - Math.cos(4 * t));
                    
                    if (t === 0) {
                        ctx.moveTo(x / 16, y / 16);
                    } else {
                        ctx.lineTo(x / 16, y / 16);
                    }
                }
                
                ctx.closePath();
                ctx.fill();
                ctx.shadowColor = 'rgba(255, 107, 157, 0.5)';
                ctx.shadowBlur = 20;
                ctx.fill();
                ctx.restore();
            }
            
            animateClick() {
                this.scale = 1.2;
                this.draw();
                setTimeout(() => {
                    this.scale = 1;
                    this.draw();
                }, 300);
            }
        }

        // Toast通知
        function showToast(message, type = 'success') {
            toastMessage.textContent = message;
            toast.className = `toast ${type}`;
            toast.style.display = 'block';
            
            setTimeout(() => {
                toast.style.display = 'none';
            }, 2000);
        }

        // 更新UI状态
        function updateUI() {
            const currentMessages = customMessages.length > 0 ? customMessages : messages;

            // 更新进度
            progressText.textContent = `${currentMessage}/6`;

            // 更新消息
            messageText.textContent = currentMessages[currentMessage] || '准备好接受我的告白了吗？';

            // 更新提示文字
            if (currentMessage === 0) {
                heartPromptText.textContent = '点击❤️开始告白';
            } else {
                const remaining = 6 - currentMessage;
                heartPromptText.textContent = remaining > 0 ? `继续点击❤️ (${remaining}次)` : '点击❤️重新开始';
            }

            // 更新音乐状态
            musicIcon.textContent = isMusicPlaying ? '🎵' : '🔇';
            musicText.textContent = isMusicPlaying ? '播放中' : '音乐';
            musicStatus.textContent = isMusicPlaying ? '播放中' : '已暂停';

            if (isMusicPlaying) {
                musicBtn.classList.add('music-active');
            } else {
                musicBtn.classList.remove('music-active');
            }
        }

        // 标签页切换
        function switchTab(tabName) {
            // 更新标签按钮状态
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.dataset.tab === tabName) {
                    btn.classList.add('active');
                }
            });

            // 更新标签内容显示
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            const targetTab = document.getElementById(`${tabName}-tab`);
            if (targetTab) {
                targetTab.classList.add('active');
            }

            currentTab = tabName;

            // 如果切换到音乐标签，刷新音乐列表
            if (tabName === 'music') {
                renderMusicList();
            }
        }

        // 设置功能
        function openSettings() {
            // 加载当前消息到输入框
            const currentMessages = customMessages.length > 0 ? customMessages : messages;
            for (let i = 0; i < 6; i++) {
                const input = document.getElementById(`message-${i + 1}`);
                if (input) {
                    input.value = currentMessages[i] || '';
                }
            }

            // 切换到消息标签
            switchTab('messages');

            settingsModal.style.display = 'flex';
            showSettingsModal = true;
        }

        function closeSettings() {
            settingsModal.style.display = 'none';
            showSettingsModal = false;

            // 停止预览音乐
            if (previewAudio) {
                previewAudio.pause();
                previewAudio = null;
            }
        }

        // 渲染音乐列表
        function renderMusicList() {
            if (!musicManager || !musicItems) return;

            const musicList = musicManager.getMusicList();
            const selectedMusic = musicManager.getSelectedMusic();

            musicItems.innerHTML = '';

            musicList.forEach(music => {
                const musicItem = document.createElement('div');
                musicItem.className = `music-item ${selectedMusic && selectedMusic.id === music.id ? 'selected' : ''}`;

                musicItem.innerHTML = `
                    <div class="music-icon">🎵</div>
                    <div class="music-info">
                        <div class="music-name">${music.name}</div>
                        <div class="music-details">${music.type === 'default' ? '默认音乐' : '用户上传'} • ${music.duration}</div>
                    </div>
                    <div class="music-controls">
                        <button class="music-btn preview" onclick="previewMusic('${music.id}')">预览</button>
                        <button class="music-btn select" onclick="selectMusic('${music.id}')" ${selectedMusic && selectedMusic.id === music.id ? 'disabled' : ''}>
                            ${selectedMusic && selectedMusic.id === music.id ? '已选择' : '选择'}
                        </button>
                        ${music.type === 'user' ? `<button class="music-btn delete" onclick="deleteMusic('${music.id}')">删除</button>` : ''}
                    </div>
                `;

                musicItems.appendChild(musicItem);
            });
        }

        // 预览音乐
        function previewMusic(musicId) {
            const music = musicManager.getMusicList().find(m => m.id === musicId);
            if (!music) return;

            // 停止当前预览
            if (previewAudio) {
                previewAudio.pause();
            }

            // 创建新的预览音频
            previewAudio = new Audio(music.url);
            previewAudio.volume = 0.3;
            previewAudio.currentTime = 0;

            previewAudio.play().then(() => {
                showToast(`正在预览：${music.name}`, 'info');

                // 10秒后自动停止预览
                setTimeout(() => {
                    if (previewAudio) {
                        previewAudio.pause();
                    }
                }, 10000);
            }).catch(error => {
                showToast('预览失败', 'error');
                console.error('Preview failed:', error);
            });
        }

        // 选择音乐
        function selectMusic(musicId) {
            if (musicManager.selectMusic(musicId)) {
                showToast('音乐已选择', 'success');
                renderMusicList();

                // 如果当前正在播放音乐，切换到新音乐
                if (isMusicPlaying) {
                    switchBackgroundMusic();
                }
            }
        }

        // 删除音乐
        function deleteMusic(musicId) {
            if (confirm('确定要删除这首音乐吗？')) {
                if (musicManager.removeMusic(musicId)) {
                    showToast('音乐已删除', 'success');
                    renderMusicList();
                }
            }
        }

        // 切换背景音乐
        function switchBackgroundMusic() {
            const newMusicUrl = musicManager.getCurrentMusicUrl();
            if (newMusicUrl && bgMusic) {
                const wasPlaying = !bgMusic.paused;
                const currentTime = bgMusic.currentTime;

                bgMusic.src = newMusicUrl;
                bgMusic.load();

                if (wasPlaying) {
                    bgMusic.play().catch(error => {
                        console.error('Failed to play new music:', error);
                        isMusicPlaying = false;
                        updateUI();
                    });
                }
            }
        }

        // 文件上传功能
        function setupFileUpload() {
            if (!uploadZone || !musicFileInput) return;

            // 点击上传区域
            uploadZone.addEventListener('click', () => {
                musicFileInput.click();
            });

            // 文件选择
            musicFileInput.addEventListener('change', (e) => {
                const files = e.target.files;
                if (files.length > 0) {
                    handleFileUpload(files[0]);
                }
            });

            // 拖拽上传
            uploadZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadZone.classList.add('dragover');
            });

            uploadZone.addEventListener('dragleave', (e) => {
                e.preventDefault();
                uploadZone.classList.remove('dragover');
            });

            uploadZone.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadZone.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFileUpload(files[0]);
                }
            });
        }

        function handleFileUpload(file) {
            showToast('正在上传音乐文件...', 'info');

            musicManager.addMusic(file).then(music => {
                showToast(`音乐"${music.name}"上传成功`, 'success');
                renderMusicList();

                // 清空文件输入
                if (musicFileInput) {
                    musicFileInput.value = '';
                }
            }).catch(error => {
                showToast(error.message, 'error');
                console.error('Upload failed:', error);
            });
        }

        function saveAllSettings() {
            let hasChanges = false;

            // 保存告白消息
            if (currentTab === 'messages' || true) {
                const newMessages = [];
                let hasCustomMessages = false;

                for (let i = 0; i < 6; i++) {
                    const input = document.getElementById(`message-${i + 1}`);
                    const value = input ? input.value.trim() : '';

                    if (value && value !== messages[i]) {
                        hasCustomMessages = true;
                    }

                    newMessages.push(value || messages[i]);
                }

                if (hasCustomMessages) {
                    customMessages = [...newMessages];
                    try {
                        localStorage.setItem('heartapp_custom_messages', JSON.stringify(customMessages));
                        hasChanges = true;
                    } catch (error) {
                        console.error('Failed to save custom messages:', error);
                    }
                }
            }

            // 音乐设置已经在选择时自动保存

            if (hasChanges) {
                showToast('设置已保存', 'success');
                updateUI();
            } else {
                showToast('没有需要保存的更改', 'info');
            }

            closeSettings();
        }

        function resetAllSettings() {
            if (currentTab === 'messages') {
                // 重置告白消息
                customMessages = [];
                try {
                    localStorage.removeItem('heartapp_custom_messages');
                } catch (error) {
                    console.error('Failed to remove custom messages:', error);
                }

                for (let i = 0; i < 6; i++) {
                    const input = document.getElementById(`message-${i + 1}`);
                    if (input) {
                        input.value = messages[i];
                    }
                }

                showToast('告白消息已恢复默认', 'success');
                updateUI();
            } else if (currentTab === 'music') {
                // 重置音乐设置
                if (confirm('确定要删除所有用户上传的音乐并恢复默认设置吗？')) {
                    try {
                        localStorage.removeItem('heartapp_user_music');
                        localStorage.removeItem('heartapp_selected_music');
                    } catch (error) {
                        console.error('Failed to reset music settings:', error);
                    }

                    // 重新初始化音乐管理器
                    musicManager = new MusicManager();
                    renderMusicList();

                    // 切换到默认音乐
                    if (isMusicPlaying) {
                        switchBackgroundMusic();
                    }

                    showToast('音乐设置已恢复默认', 'success');
                }
            }
        }

        // 加载用户偏好
        function loadUserPreferences() {
            try {
                const saved = localStorage.getItem('heartapp_custom_messages');
                if (saved) {
                    const savedMessages = JSON.parse(saved);
                    if (savedMessages && savedMessages.length === 6) {
                        customMessages = [...savedMessages];
                    }
                }
                console.log('User preferences loaded');
            } catch (error) {
                console.error('Failed to load user preferences:', error);
            }
        }

        // 心形点击处理
        function handleHeartClick(event) {
            if (isAnimating) return;

            console.log('Heart clicked, current message:', currentMessage);

            // 第一次点击时尝试播放音乐
            if (currentMessage === 0 && !isMusicPlaying) {
                toggleMusic();
            }

            isAnimating = true;

            // 心形动画
            if (heartRenderer) {
                heartRenderer.animateClick();
            }

            // 创建爆炸效果
            createExplosionEffect(event);

            // 创建心形粒子
            createHeartParticles();

            // 更新消息
            const currentMessages = customMessages.length > 0 ? customMessages : messages;
            currentMessage = (currentMessage + 1) % currentMessages.length;

            // 更新UI
            updateUI();

            setTimeout(() => {
                isAnimating = false;
            }, 600);
        }

        // 创建爆炸效果
        function createExplosionEffect(event) {
            if (!heartCanvas) return;

            const rect = heartCanvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            // 创建波纹效果
            const ripple = document.createElement('div');
            ripple.style.cssText = `
                position: absolute;
                left: ${rect.left + x - 50}px;
                top: ${rect.top + y - 50}px;
                width: 100px;
                height: 100px;
                border-radius: 50%;
                background: radial-gradient(circle, rgba(255,107,157,0.8) 0%, rgba(255,107,157,0.3) 50%, transparent 100%);
                transform: scale(0);
                pointer-events: none;
                z-index: 1000;
            `;

            document.body.appendChild(ripple);

            // 爆炸动画
            ripple.animate([
                { transform: 'scale(0)', opacity: 1 },
                { transform: 'scale(3)', opacity: 0 }
            ], {
                duration: 800,
                easing: 'ease-out'
            }).onfinish = () => {
                if (document.body.contains(ripple)) {
                    document.body.removeChild(ripple);
                }
            };

            // 创建多个小爆炸粒子
            for (let i = 0; i < 12; i++) {
                createExplosionParticle(rect.left + x, rect.top + y, i);
            }
        }

        // 创建爆炸粒子
        function createExplosionParticle(centerX, centerY, index) {
            const particle = document.createElement('div');
            const colors = ['#ff6b9d', '#ff8fab', '#ffa8b8', '#ffb3c1', '#ffc1cc'];
            const color = colors[index % colors.length];

            particle.style.cssText = `
                position: absolute;
                left: ${centerX}px;
                top: ${centerY}px;
                width: 8px;
                height: 8px;
                background: ${color};
                border-radius: 50%;
                pointer-events: none;
                z-index: 1000;
            `;

            document.body.appendChild(particle);

            const angle = (index / 12) * Math.PI * 2;
            const distance = 80 + Math.random() * 40;
            const endX = centerX + Math.cos(angle) * distance;
            const endY = centerY + Math.sin(angle) * distance - 30;

            particle.animate([
                {
                    transform: 'translate(-50%, -50%) scale(1)',
                    opacity: 1
                },
                {
                    transform: `translate(${endX - centerX}px, ${endY - centerY}px) scale(0)`,
                    opacity: 0
                }
            ], {
                duration: 1000 + Math.random() * 500,
                easing: 'ease-out'
            }).onfinish = () => {
                if (document.body.contains(particle)) {
                    document.body.removeChild(particle);
                }
            };
        }

        // 创建心形粒子
        function createHeartParticles() {
            if (!heartCanvas) return;

            const rect = heartCanvas.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;

            // 创建多个心形粒子
            for (let i = 0; i < 8; i++) {
                const particle = document.createElement('div');
                const hearts = ['❤️', '💖', '💕', '💗', '💓'];
                particle.textContent = hearts[i % hearts.length];
                particle.style.cssText = `
                    position: absolute;
                    left: ${centerX}px;
                    top: ${centerY}px;
                    font-size: ${1.2 + Math.random() * 0.8}rem;
                    pointer-events: none;
                    z-index: 1000;
                `;

                document.body.appendChild(particle);

                const angle = (i / 8) * Math.PI * 2;
                const distance = 120 + Math.random() * 60;
                const endX = centerX + Math.cos(angle) * distance;
                const endY = centerY + Math.sin(angle) * distance - 80;

                particle.animate([
                    {
                        transform: 'translate(-50%, -50%) scale(1) rotate(0deg)',
                        opacity: 1
                    },
                    {
                        transform: `translate(${endX - centerX}px, ${endY - centerY}px) scale(0.3) rotate(360deg)`,
                        opacity: 0
                    }
                ], {
                    duration: 2000 + Math.random() * 1000,
                    easing: 'ease-out'
                }).onfinish = () => {
                    if (document.body.contains(particle)) {
                        document.body.removeChild(particle);
                    }
                };
            }
        }

        // 音乐控制
        async function toggleMusic() {
            console.log('Toggle music called, current state:', isMusicPlaying);

            if (!bgMusic) {
                console.error('Audio element not found');
                showToast('音频元素未找到', 'error');
                return;
            }

            try {
                if (isMusicPlaying) {
                    console.log('Pausing music');
                    bgMusic.pause();
                    isMusicPlaying = false;
                    showToast('音乐已暂停', 'success');
                } else {
                    console.log('Playing music');

                    // 确保使用当前选择的音乐
                    const currentMusicUrl = musicManager ? musicManager.getCurrentMusicUrl() : null;
                    if (currentMusicUrl && bgMusic.src !== currentMusicUrl) {
                        bgMusic.src = currentMusicUrl;
                        bgMusic.load();
                    }

                    bgMusic.volume = 0.3;
                    await bgMusic.play();
                    isMusicPlaying = true;

                    const selectedMusic = musicManager ? musicManager.getSelectedMusic() : null;
                    const musicName = selectedMusic ? selectedMusic.name : '背景音乐';
                    showToast(`正在播放：${musicName}`, 'success');
                }
                updateUI();
            } catch (error) {
                console.error('Music toggle error:', error);
                showToast(`播放失败: ${error.message}`, 'error');
            }
        }

        // 分享功能
        async function shareApp() {
            try {
                if (navigator.share) {
                    await navigator.share({
                        title: '心动告白',
                        text: '用这个浪漫的告白应用表达你的心意吧！',
                        url: window.location.href
                    });
                } else {
                    await navigator.clipboard.writeText(window.location.href);
                    showToast('链接已复制到剪贴板', 'success');
                }
            } catch (error) {
                showToast('分享失败', 'error');
            }
        }

        // 截图功能
        function takeScreenshot() {
            const flash = document.createElement('div');
            flash.className = 'screenshot-flash';
            document.body.appendChild(flash);
            
            setTimeout(() => {
                document.body.removeChild(flash);
            }, 300);
            
            showToast('请使用浏览器的截图功能', 'warning');
        }

        // 调试音频
        function debugAudio() {
            console.log('🔧 Audio debug info:');
            console.log('- Element:', bgMusic);
            console.log('- Src:', bgMusic?.src);
            console.log('- Paused:', bgMusic?.paused);
            console.log('- Volume:', bgMusic?.volume);
            console.log('- ReadyState:', bgMusic?.readyState);
            console.log('- NetworkState:', bgMusic?.networkState);
            
            showToast(`音频调试: ${bgMusic ? '元素存在' : '元素不存在'}`, bgMusic ? 'success' : 'error');
        }

        // 初始化应用
        function initApp() {
            console.log('Initializing Heart Confession App (Native JS)');

            // 初始化音乐管理器
            musicManager = new MusicManager();
            console.log('Music manager initialized');

            // 初始化心形渲染器
            if (heartCanvas) {
                heartRenderer = new SimpleHeartRenderer(heartCanvas);
                heartCanvas.addEventListener('click', handleHeartClick);
                console.log('Heart renderer initialized');
            }

            // 初始化音频
            if (bgMusic) {
                // 设置初始音乐源
                const initialMusicUrl = musicManager.getCurrentMusicUrl();
                if (initialMusicUrl) {
                    bgMusic.src = initialMusicUrl;
                }

                bgMusic.volume = 0.3;

                bgMusic.addEventListener('play', () => {
                    console.log('Audio play event');
                    isMusicPlaying = true;
                    updateUI();
                });

                bgMusic.addEventListener('pause', () => {
                    console.log('Audio pause event');
                    isMusicPlaying = false;
                    updateUI();
                });

                bgMusic.addEventListener('error', (e) => {
                    console.error('Audio error:', e);
                    showToast('音频加载失败', 'error');
                });

                console.log('Audio initialized');
            }

            // 绑定基本事件
            if (musicBtn) musicBtn.addEventListener('click', toggleMusic);
            if (shareBtn) shareBtn.addEventListener('click', shareApp);
            if (screenshotBtn) screenshotBtn.addEventListener('click', takeScreenshot);
            if (settingsBtn) settingsBtn.addEventListener('click', openSettings);
            if (debugBtn) debugBtn.addEventListener('click', debugAudio);

            // 设置模态框事件
            if (saveSettingsBtn) saveSettingsBtn.addEventListener('click', saveAllSettings);
            if (cancelSettingsBtn) cancelSettingsBtn.addEventListener('click', closeSettings);
            if (resetSettingsBtn) resetSettingsBtn.addEventListener('click', resetAllSettings);

            // 标签页切换事件
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    switchTab(btn.dataset.tab);
                });
            });

            // 点击模态框背景关闭
            if (settingsModal) {
                settingsModal.addEventListener('click', (e) => {
                    if (e.target === settingsModal) {
                        closeSettings();
                    }
                });
            }

            // 初始化文件上传
            setupFileUpload();

            // 加载用户偏好
            loadUserPreferences();

            // 初始化UI
            updateUI();

            console.log('✅ App initialized successfully');
            showToast('应用已加载，点击心形开始告白！', 'success');
        }

        // 页面加载完成后初始化
        window.addEventListener('load', initApp);
        
        console.log('🚀 Heart Confession App (Native JS) script loaded');
    </script>
</body>
</html>
