# 心动告白 - Heart Confession App

一个基于Vue.js 3开发的浪漫告白移动端响应式网页应用。

## 🚀 快速开始

### 方法一：使用启动脚本（推荐）
1. 双击 `start-server.bat` 文件
2. 等待服务器启动
3. 在浏览器中访问 `http://localhost:8009`

### 方法二：手动启动
1. 打开命令行，进入项目目录
2. 运行：`python -m http.server 8009`
3. 在浏览器中访问 `http://localhost:8009`

### 方法三：直接打开
- 双击 `index.html` 文件（功能可能受限）

## 📁 文件结构

```
C:\Users\<USER>\Documents\augment-projects\123\
├── index.html              # 主应用页面
├── test.html              # 测试页面
├── start-server.bat       # 启动脚本
├── manifest.json          # PWA清单
├── css\
│   └── style.css          # 样式文件
├── js\
│   └── vue-app.js         # Vue.js 3应用
└── README.md              # 说明文档
```

## ✨ 功能特性

- **心形点击动画** - 炫酷的爆炸和粒子效果
- **递进式告白** - 6句精心设计的告白语句
- **自定义告白** - 可以修改告白内容
- **响应式设计** - 完美适配移动设备
- **PWA支持** - 可安装到桌面
- **分享功能** - 一键分享给朋友

## 🎮 使用说明

1. **开始告白** - 点击中央的心形图案
2. **查看消息** - 每次点击显示一句告白
3. **自定义** - 点击右上角设置按钮修改告白内容
4. **分享** - 点击分享按钮分享给朋友

## 🔧 技术栈

- Vue.js 3 (Composition API)
- HTML5 Canvas
- CSS3 动画
- 响应式设计
- PWA技术

## 📱 兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+
- 移动端浏览器

## 💝 告白语句

默认包含6句递进式告白：
1. "你知道吗？你的笑容是我见过最美的风景。"
2. "每当我看到你，我的心跳都会不由自主地加速。"
3. "和你在一起的每一刻，都让我感到无比幸福。"
4. "你的温柔如春风，轻抚着我的心田。"
5. "我想牵着你的手，走过人生的每一个春夏秋冬。"
6. "我爱你，不只是今天，而是每一个明天。"

---

💝 **用这个浪漫的应用表达你的心意吧！** 💝
