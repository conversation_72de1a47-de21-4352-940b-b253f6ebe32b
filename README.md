# 心动告白 - Heart Confession App

一个基于Vue.js 3开发的浪漫告白移动端响应式网页应用。

## 🚀 快速开始

### 方法一：使用启动脚本（推荐）
1. 双击 `start-server.bat` 文件
2. 等待服务器启动
3. 在浏览器中访问 `http://localhost:8009`

### 方法二：手动启动
1. 打开命令行，进入项目目录
2. 运行：`python -m http.server 8009`
3. 在浏览器中访问 `http://localhost:8009`

### 方法三：直接打开
- 双击 `index.html` 文件（功能可能受限）

## 🔗 访问链接

- **主应用**：http://localhost:8009
- **Vue.js测试**：http://localhost:8009/test.html
- **音乐测试**：http://localhost:8009/music-test.html

## 📁 文件结构

```
C:\Users\<USER>\Documents\augment-projects\123\
├── index.html              # 主应用页面
├── test.html              # Vue.js功能测试页面
├── music-test.html        # 音乐功能测试页面
├── start-server.bat       # 启动脚本
├── manifest.json          # PWA清单
├── css\
│   └── style.css          # 样式文件
├── js\
│   └── vue-app.js         # Vue.js 3应用
├── assets\
│   └── audio\
│       └── romantic-musi.mp3  # 背景音乐文件
└── README.md              # 说明文档
```

## ✨ 功能特性

- **心形点击动画** - 炫酷的爆炸和粒子效果
- **递进式告白** - 6句精心设计的告白语句
- **背景音乐** - 浪漫的背景音乐营造氛围
- **自定义告白** - 可以修改告白内容
- **响应式设计** - 完美适配移动设备
- **PWA支持** - 可安装到桌面
- **分享功能** - 一键分享给朋友

## 🎵 音乐功能

### 音乐文件
- 当前使用：`assets/audio/romantic-musi.mp3`
- 支持格式：MP3, WAV, OGG等浏览器支持的音频格式
- 音量设置：自动调节为30%，避免过大声音

### 播放控制
- **自动播放**：首次点击心形时自动尝试播放
- **手动控制**：点击音乐按钮（🎵）开启/关闭
- **循环播放**：音乐会自动循环播放
- **状态指示**：按钮会显示当前播放状态

### 更换音乐
如需更换背景音乐：
1. 将新的音频文件放入 `assets/audio/` 文件夹
2. 重命名为 `romantic-musi.mp3` 或修改HTML中的文件路径
3. 刷新页面即可使用新音乐

## 🎮 使用说明

1. **开始告白** - 点击中央的心形图案
2. **查看消息** - 每次点击显示一句告白
3. **背景音乐** - 点击音乐按钮开启/关闭背景音乐
   - 首次点击心形时会自动尝试播放音乐
   - 如果自动播放失败，请手动点击音乐按钮
4. **自定义** - 点击右上角设置按钮修改告白内容
5. **分享** - 点击分享按钮分享给朋友

## 🔧 技术栈

- Vue.js 3 (Composition API)
- HTML5 Canvas
- CSS3 动画
- 响应式设计
- PWA技术

## 📱 兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+
- 移动端浏览器

## 💝 告白语句

默认包含6句递进式告白：
1. "你知道吗？你的笑容是我见过最美的风景。"
2. "每当我看到你，我的心跳都会不由自主地加速。"
3. "和你在一起的每一刻，都让我感到无比幸福。"
4. "你的温柔如春风，轻抚着我的心田。"
5. "我想牵着你的手，走过人生的每一个春夏秋冬。"
6. "我爱你，不只是今天，而是每一个明天。"

---

💝 **用这个浪漫的应用表达你的心意吧！** 💝
