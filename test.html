<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue.js 3 Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #ff6b9d 0%, #c44569 50%, #f8b500 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0;
        }
        .test-container {
            background: rgba(255, 255, 255, 0.9);
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }
        .heart {
            font-size: 4rem;
            color: #ff6b9d;
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        .heart:hover {
            transform: scale(1.1);
        }
        .message {
            margin: 20px 0;
            font-size: 1.2rem;
            color: #2d3436;
        }
        .counter {
            font-size: 1rem;
            color: #636e72;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h1>Vue.js 3 测试</h1>
            <div class="heart" @click="clickHeart">❤️</div>
            <div class="message">{{ message }}</div>
            <div class="counter">点击次数: {{ clickCount }}</div>
        </div>
    </div>

    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script>
        const { createApp, ref } = Vue;

        createApp({
            setup() {
                const clickCount = ref(0);
                const message = ref('点击心形开始测试！');
                
                const messages = [
                    '点击心形开始测试！',
                    'Vue.js 3 正在工作！',
                    '响应式数据更新成功！',
                    '事件处理正常！',
                    '测试完成！'
                ];

                const clickHeart = () => {
                    clickCount.value++;
                    const messageIndex = Math.min(clickCount.value, messages.length - 1);
                    message.value = messages[messageIndex];
                };

                return {
                    clickCount,
                    message,
                    clickHeart
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
