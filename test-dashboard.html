<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音乐功能测试仪表板</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            color: #333;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        .header p {
            color: #666;
            font-size: 1.2rem;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .test-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .test-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-color: #007bff;
        }
        .test-card.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }
        .test-card.success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
            border-color: #56ab2f;
        }
        .test-card.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-color: #f093fb;
        }
        .test-card h3 {
            margin: 0 0 15px 0;
            font-size: 1.5rem;
        }
        .test-card p {
            margin: 0 0 15px 0;
            line-height: 1.6;
        }
        .test-card .features {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }
        .test-card .features li {
            padding: 5px 0;
            position: relative;
            padding-left: 20px;
        }
        .test-card .features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
        .test-card.primary .features li:before,
        .test-card.success .features li:before,
        .test-card.warning .features li:before {
            color: rgba(255,255,255,0.8);
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.success:hover {
            background: #1e7e34;
        }
        .btn.warning {
            background: #ffc107;
            color: #000;
        }
        .btn.warning:hover {
            background: #e0a800;
        }
        .status-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .quick-test {
            text-align: center;
            background: linear-gradient(135deg, #ff6b9d 0%, #c44569 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
        }
        .quick-test h2 {
            margin: 0 0 20px 0;
        }
        .quick-test-btn {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 10px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s;
        }
        .quick-test-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.05);
        }
        .file-info {
            background: #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .file-info h3 {
            margin: 0 0 15px 0;
            color: #495057;
        }
        .file-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 10px;
        }
        .file-item {
            background: white;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .file-item strong {
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎵 音乐功能测试仪表板</h1>
            <p>全面诊断和测试心动告白应用的音乐功能</p>
        </div>

        <div class="quick-test">
            <h2>🚀 快速测试</h2>
            <p>点击下面的按钮进行快速音乐功能测试</p>
            <button class="quick-test-btn" onclick="window.open('vue-check.html', '_blank')">
                🔍 检查Vue.js状态
            </button>
            <button class="quick-test-btn" onclick="window.open('index-no-vue.html', '_blank')">
                ⚡ 原生JS版本
            </button>
            <button class="quick-test-btn" onclick="window.open('simple-music-test.html', '_blank')">
                ▶️ 立即测试音乐
            </button>
        </div>

        <div class="test-grid">
            <div class="test-card warning" onclick="window.open('vue-check.html', '_blank')">
                <h3>🔍 Vue.js 状态检查</h3>
                <p>检查Vue.js是否正确加载，这是音乐功能的关键</p>
                <ul class="features">
                    <li>Vue.js加载状态检查</li>
                    <li>本地文件vs CDN检查</li>
                    <li>Vue应用实例测试</li>
                    <li>响应式功能验证</li>
                </ul>
                <a href="vue-check.html" class="btn warning">检查Vue.js</a>
            </div>

            <div class="test-card success" onclick="window.open('index-no-vue.html', '_blank')">
                <h3>⚡ 原生JS版本</h3>
                <p>不依赖Vue.js的原生JavaScript实现，确保功能可用</p>
                <ul class="features">
                    <li>无需Vue.js依赖</li>
                    <li>原生事件处理</li>
                    <li>完整音乐功能</li>
                    <li>调试面板</li>
                </ul>
                <a href="index-no-vue.html" class="btn success">使用原生版</a>
            </div>

            <div class="test-card primary" onclick="window.open('simple-music-test.html', '_blank')">
                <h3>🎵 简单音乐测试</h3>
                <p>最直接的音乐功能测试，大按钮设计，易于操作</p>
                <ul class="features">
                    <li>一键播放/暂停</li>
                    <li>音量控制</li>
                    <li>实时控制台日志</li>
                    <li>详细错误信息</li>
                </ul>
                <a href="simple-music-test.html" class="btn">开始测试</a>
            </div>

            <div class="test-card success" onclick="window.open('comprehensive-test.html', '_blank')">
                <h3>🔧 综合功能测试</h3>
                <p>全面的系统诊断工具，检查所有可能的问题</p>
                <ul class="features">
                    <li>文件存在性检查</li>
                    <li>浏览器兼容性测试</li>
                    <li>网络连接测试</li>
                    <li>Vue.js状态检查</li>
                    <li>自动修复尝试</li>
                </ul>
                <a href="comprehensive-test.html" class="btn success">开始诊断</a>
            </div>

            <div class="test-card warning" onclick="window.open('music-fix.html', '_blank')">
                <h3>🎼 音乐修复版</h3>
                <p>专门的音乐功能修复版本，包含心形互动</p>
                <ul class="features">
                    <li>心形点击测试</li>
                    <li>状态可视化</li>
                    <li>音频信息显示</li>
                    <li>多种测试按钮</li>
                </ul>
                <a href="music-fix.html" class="btn warning">测试修复版</a>
            </div>

            <div class="test-card" onclick="window.open('index-fixed.html', '_blank')">
                <h3>💝 主应用修复版</h3>
                <p>完整的心动告白应用修复版本</p>
                <ul class="features">
                    <li>完整告白功能</li>
                    <li>音乐集成</li>
                    <li>调试面板</li>
                    <li>Vue.js 3实现</li>
                </ul>
                <a href="index-fixed.html" class="btn">体验应用</a>
            </div>

            <div class="test-card" onclick="window.open('music-test.html', '_blank')">
                <h3>🎚️ 专业音频测试</h3>
                <p>专业级音频控制和测试界面</p>
                <ul class="features">
                    <li>精确音量控制</li>
                    <li>播放进度显示</li>
                    <li>音频元数据</li>
                    <li>专业控制面板</li>
                </ul>
                <a href="music-test.html" class="btn">专业测试</a>
            </div>

            <div class="test-card" onclick="window.open('debug.html', '_blank')">
                <h3>🐛 调试工具</h3>
                <p>开发者调试工具，系统性检查各项功能</p>
                <ul class="features">
                    <li>分步骤测试</li>
                    <li>详细日志输出</li>
                    <li>系统信息检查</li>
                    <li>问题定位</li>
                </ul>
                <a href="debug.html" class="btn">调试工具</a>
            </div>
        </div>

        <div class="status-section">
            <h3>📊 当前状态</h3>
            <div id="status-info">
                <p><strong>服务器地址:</strong> <span id="server-url"></span></p>
                <p><strong>浏览器:</strong> <span id="browser-info"></span></p>
                <p><strong>音频支持:</strong> <span id="audio-support"></span></p>
                <p><strong>当前时间:</strong> <span id="current-time"></span></p>
            </div>
        </div>

        <div class="file-info">
            <h3>📁 项目文件结构</h3>
            <div class="file-list">
                <div class="file-item">
                    <strong>主应用:</strong><br>
                    index.html (原版)<br>
                    index-fixed.html (修复版)
                </div>
                <div class="file-item">
                    <strong>音乐测试:</strong><br>
                    simple-music-test.html<br>
                    music-fix.html<br>
                    music-test.html
                </div>
                <div class="file-item">
                    <strong>诊断工具:</strong><br>
                    comprehensive-test.html<br>
                    debug.html<br>
                    test-dashboard.html
                </div>
                <div class="file-item">
                    <strong>音频文件:</strong><br>
                    assets/audio/romantic-musi.mp3
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
            <h3>🎯 测试建议</h3>
            <p><strong>步骤1:</strong> 先使用"简单音乐测试"确认基本功能</p>
            <p><strong>步骤2:</strong> 如果有问题，使用"综合功能测试"进行诊断</p>
            <p><strong>步骤3:</strong> 查看控制台日志获取详细错误信息</p>
            <p><strong>步骤4:</strong> 尝试不同浏览器测试兼容性</p>
        </div>
    </div>

    <script>
        // 更新状态信息
        function updateStatus() {
            document.getElementById('server-url').textContent = window.location.origin;
            document.getElementById('browser-info').textContent = navigator.userAgent.split(' ').slice(-2).join(' ');
            
            // 检查音频支持
            const audio = document.createElement('audio');
            const mp3Support = audio.canPlayType('audio/mpeg');
            document.getElementById('audio-support').textContent = mp3Support ? `MP3: ${mp3Support}` : '不支持MP3';
            
            // 更新时间
            document.getElementById('current-time').textContent = new Date().toLocaleString();
        }

        // 页面加载时更新状态
        window.addEventListener('load', updateStatus);
        
        // 每分钟更新时间
        setInterval(() => {
            document.getElementById('current-time').textContent = new Date().toLocaleString();
        }, 60000);

        console.log('🎵 音乐功能测试仪表板已加载');
        console.log('📋 可用测试页面:');
        console.log('  - simple-music-test.html (推荐首选)');
        console.log('  - comprehensive-test.html (完整诊断)');
        console.log('  - music-fix.html (修复版测试)');
        console.log('  - index-fixed.html (主应用修复版)');
    </script>
</body>
</html>
