<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>综合音乐功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #ff6b9d 0%, #c44569 50%, #f8b500 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .test-section.success {
            border-color: #28a745;
            background: #d4edda;
        }
        .test-section.error {
            border-color: #dc3545;
            background: #f8d7da;
        }
        .test-section.warning {
            border-color: #ffc107;
            background: #fff3cd;
        }
        .btn {
            background: #ff6b9d;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background: #e55a87;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.error {
            background: #dc3545;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .audio-controls {
            display: flex;
            gap: 10px;
            align-items: center;
            margin: 10px 0;
        }
        .volume-slider {
            width: 150px;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #ddd;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: #ff6b9d;
            width: 0%;
            transition: width 0.1s;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 综合音乐功能测试系统</h1>
        
        <!-- 基础信息 -->
        <div class="test-section" id="basic-info">
            <h3>📋 基础信息</h3>
            <p><strong>当前URL:</strong> <span id="current-url"></span></p>
            <p><strong>用户代理:</strong> <span id="user-agent"></span></p>
            <p><strong>音频支持:</strong> <span id="audio-support"></span></p>
            <p><strong>自动播放策略:</strong> <span id="autoplay-policy"></span></p>
        </div>

        <!-- 文件检查 -->
        <div class="test-section" id="file-check">
            <h3>📁 文件检查</h3>
            <button class="btn" onclick="checkAudioFile()">检查音频文件</button>
            <div id="file-status"></div>
        </div>

        <!-- HTML元素检查 -->
        <div class="test-section" id="element-check">
            <h3>🔍 HTML元素检查</h3>
            <button class="btn" onclick="checkAudioElements()">检查音频元素</button>
            <div id="element-status"></div>
        </div>

        <!-- 音频播放测试 -->
        <div class="test-section" id="playback-test">
            <h3>🎵 音频播放测试</h3>
            <div class="audio-controls">
                <button class="btn" id="play-btn" onclick="testPlay()">播放</button>
                <button class="btn" id="pause-btn" onclick="testPause()">暂停</button>
                <button class="btn" onclick="testStop()">停止</button>
                <input type="range" class="volume-slider" id="volume-slider" min="0" max="1" step="0.1" value="0.3" onchange="setVolume(this.value)">
                <span id="volume-display">30%</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            <div id="playback-status"></div>
        </div>

        <!-- Vue.js测试 -->
        <div class="test-section" id="vue-test">
            <h3>⚡ Vue.js测试</h3>
            <button class="btn" onclick="testVue()">测试Vue.js</button>
            <div id="vue-status"></div>
        </div>

        <!-- 浏览器兼容性测试 -->
        <div class="test-section" id="compatibility-test">
            <h3>🌐 浏览器兼容性测试</h3>
            <button class="btn" onclick="testCompatibility()">测试兼容性</button>
            <div id="compatibility-status"></div>
        </div>

        <!-- 网络测试 -->
        <div class="test-section" id="network-test">
            <h3>🌐 网络测试</h3>
            <button class="btn" onclick="testNetwork()">测试网络连接</button>
            <div id="network-status"></div>
        </div>

        <!-- 实时日志 -->
        <div class="test-section">
            <h3>📝 实时日志</h3>
            <button class="btn" onclick="clearLog()">清除日志</button>
            <div class="log" id="log"></div>
        </div>

        <!-- 一键修复 -->
        <div class="test-section">
            <h3>🔧 一键修复</h3>
            <button class="btn" onclick="runAllTests()" style="background: #007bff;">运行所有测试</button>
            <button class="btn" onclick="attemptFix()" style="background: #28a745;">尝试自动修复</button>
        </div>

        <!-- 导航链接 -->
        <div style="text-align: center; margin-top: 20px;">
            <a href="index.html">原版主应用</a> | 
            <a href="index-fixed.html">修复版主应用</a> | 
            <a href="music-fix.html">音乐测试版</a>
        </div>
    </div>

    <!-- 测试用音频元素 -->
    <audio id="test-audio" loop preload="metadata">
        <source src="assets/audio/romantic-musi.mp3" type="audio/mpeg">
        <source src="assets/audio/romantic-musi.mp3" type="audio/mp3">
    </audio>

    <script>
        let testAudio = null;
        let testResults = {};

        // 日志系统
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#f00' : type === 'success' ? '#0f0' : type === 'warning' ? '#ff0' : '#0f0';
            logElement.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type.toUpperCase()}]`, message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function updateSection(sectionId, status) {
            const section = document.getElementById(sectionId);
            section.className = `test-section ${status}`;
        }

        // 初始化
        function init() {
            testAudio = document.getElementById('test-audio');
            
            // 基础信息
            document.getElementById('current-url').textContent = window.location.href;
            document.getElementById('user-agent').textContent = navigator.userAgent.substring(0, 100) + '...';
            
            // 音频支持检查
            const audio = document.createElement('audio');
            const mp3Support = audio.canPlayType('audio/mpeg');
            document.getElementById('audio-support').textContent = mp3Support ? `MP3: ${mp3Support}` : '不支持MP3';
            
            // 自动播放策略
            if ('getAutoplayPolicy' in HTMLMediaElement) {
                document.getElementById('autoplay-policy').textContent = HTMLMediaElement.getAutoplayPolicy(testAudio);
            } else {
                document.getElementById('autoplay-policy').textContent = '无法检测';
            }

            // 音频事件监听
            setupAudioEvents();
            
            log('测试系统初始化完成', 'success');
        }

        function setupAudioEvents() {
            if (!testAudio) return;

            testAudio.addEventListener('loadstart', () => log('开始加载音频'));
            testAudio.addEventListener('loadeddata', () => log('音频数据已加载'));
            testAudio.addEventListener('canplay', () => log('音频可以播放', 'success'));
            testAudio.addEventListener('canplaythrough', () => log('音频完全加载', 'success'));
            testAudio.addEventListener('play', () => {
                log('音频开始播放', 'success');
                document.getElementById('play-btn').textContent = '播放中...';
                document.getElementById('play-btn').disabled = true;
                document.getElementById('pause-btn').disabled = false;
            });
            testAudio.addEventListener('pause', () => {
                log('音频已暂停', 'warning');
                document.getElementById('play-btn').textContent = '播放';
                document.getElementById('play-btn').disabled = false;
                document.getElementById('pause-btn').disabled = true;
            });
            testAudio.addEventListener('error', (e) => {
                log(`音频错误: ${e.target.error?.message || '未知错误'}`, 'error');
                updateSection('playback-test', 'error');
            });
            testAudio.addEventListener('timeupdate', () => {
                if (testAudio.duration) {
                    const progress = (testAudio.currentTime / testAudio.duration) * 100;
                    document.getElementById('progress-fill').style.width = progress + '%';
                }
            });
        }

        // 文件检查
        async function checkAudioFile() {
            log('检查音频文件...');
            const statusDiv = document.getElementById('file-status');
            
            try {
                const response = await fetch('assets/audio/romantic-musi.mp3');
                if (response.ok) {
                    const blob = await response.blob();
                    const sizeMB = (blob.size / 1024 / 1024).toFixed(2);
                    const message = `✅ 音频文件存在 (${sizeMB} MB, ${response.status})`;
                    statusDiv.innerHTML = `<div class="status" style="background: #d4edda; color: #155724;">${message}</div>`;
                    log(message, 'success');
                    updateSection('file-check', 'success');
                    testResults.fileCheck = true;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                const message = `❌ 音频文件检查失败: ${error.message}`;
                statusDiv.innerHTML = `<div class="status" style="background: #f8d7da; color: #721c24;">${message}</div>`;
                log(message, 'error');
                updateSection('file-check', 'error');
                testResults.fileCheck = false;
            }
        }

        // HTML元素检查
        function checkAudioElements() {
            log('检查HTML音频元素...');
            const statusDiv = document.getElementById('element-status');
            let html = '';

            // 检查测试音频元素
            if (testAudio) {
                html += `<p>✅ 测试音频元素存在</p>`;
                html += `<p>源数量: ${testAudio.children.length}</p>`;
                html += `<p>当前源: ${testAudio.currentSrc || '无'}</p>`;
                html += `<p>网络状态: ${testAudio.networkState}</p>`;
                html += `<p>就绪状态: ${testAudio.readyState}</p>`;
                log('测试音频元素检查通过', 'success');
                testResults.elementCheck = true;
            } else {
                html += `<p>❌ 测试音频元素不存在</p>`;
                log('测试音频元素不存在', 'error');
                testResults.elementCheck = false;
            }

            // 检查主应用音频元素
            const mainAudio = document.getElementById('bg-music');
            if (mainAudio) {
                html += `<p>✅ 主应用音频元素存在</p>`;
            } else {
                html += `<p>⚠️ 主应用音频元素不存在（正常，因为不在主页面）</p>`;
            }

            statusDiv.innerHTML = html;
            updateSection('element-check', testResults.elementCheck ? 'success' : 'error');
        }

        // 播放测试
        async function testPlay() {
            log('尝试播放音频...');
            if (!testAudio) {
                log('音频元素不存在', 'error');
                return;
            }

            try {
                testAudio.volume = parseFloat(document.getElementById('volume-slider').value);
                await testAudio.play();
                log('音频播放成功', 'success');
                updateSection('playback-test', 'success');
                testResults.playbackTest = true;
            } catch (error) {
                log(`音频播放失败: ${error.message}`, 'error');
                updateSection('playback-test', 'error');
                testResults.playbackTest = false;
                
                // 详细错误分析
                if (error.name === 'NotAllowedError') {
                    log('浏览器阻止了自动播放，需要用户交互', 'warning');
                } else if (error.name === 'NotSupportedError') {
                    log('音频格式不支持', 'error');
                } else if (error.name === 'AbortError') {
                    log('播放被中止', 'warning');
                }
            }
        }

        function testPause() {
            if (testAudio) {
                testAudio.pause();
                log('音频已暂停');
            }
        }

        function testStop() {
            if (testAudio) {
                testAudio.pause();
                testAudio.currentTime = 0;
                log('音频已停止');
            }
        }

        function setVolume(value) {
            if (testAudio) {
                testAudio.volume = value;
                document.getElementById('volume-display').textContent = Math.round(value * 100) + '%';
                log(`音量设置为 ${Math.round(value * 100)}%`);
            }
        }

        // Vue.js测试
        function testVue() {
            log('测试Vue.js...');
            const statusDiv = document.getElementById('vue-status');
            
            if (typeof Vue !== 'undefined') {
                statusDiv.innerHTML = `<p>✅ Vue.js已加载 (版本: ${Vue.version || '未知'})</p>`;
                log('Vue.js检查通过', 'success');
                updateSection('vue-test', 'success');
                testResults.vueTest = true;
            } else {
                statusDiv.innerHTML = `<p>❌ Vue.js未加载</p>`;
                log('Vue.js未加载', 'error');
                updateSection('vue-test', 'error');
                testResults.vueTest = false;
            }
        }

        // 兼容性测试
        function testCompatibility() {
            log('测试浏览器兼容性...');
            const statusDiv = document.getElementById('compatibility-status');
            let html = '';
            let allGood = true;

            // 检查关键API
            const apis = [
                { name: 'Audio API', check: () => typeof Audio !== 'undefined' },
                { name: 'Promise', check: () => typeof Promise !== 'undefined' },
                { name: 'Fetch API', check: () => typeof fetch !== 'undefined' },
                { name: 'ES6 Classes', check: () => { try { eval('class Test {}'); return true; } catch { return false; } } },
                { name: 'Arrow Functions', check: () => { try { eval('() => {}'); return true; } catch { return false; } } }
            ];

            apis.forEach(api => {
                const supported = api.check();
                html += `<p>${supported ? '✅' : '❌'} ${api.name}</p>`;
                if (!supported) allGood = false;
            });

            statusDiv.innerHTML = html;
            updateSection('compatibility-test', allGood ? 'success' : 'error');
            testResults.compatibilityTest = allGood;
            log(`兼容性测试${allGood ? '通过' : '失败'}`, allGood ? 'success' : 'error');
        }

        // 网络测试
        async function testNetwork() {
            log('测试网络连接...');
            const statusDiv = document.getElementById('network-status');
            
            try {
                const start = Date.now();
                const response = await fetch('assets/audio/romantic-musi.mp3', { method: 'HEAD' });
                const duration = Date.now() - start;
                
                if (response.ok) {
                    const message = `✅ 网络连接正常 (${duration}ms, ${response.status})`;
                    statusDiv.innerHTML = `<p>${message}</p>`;
                    log(message, 'success');
                    updateSection('network-test', 'success');
                    testResults.networkTest = true;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                const message = `❌ 网络连接失败: ${error.message}`;
                statusDiv.innerHTML = `<p>${message}</p>`;
                log(message, 'error');
                updateSection('network-test', 'error');
                testResults.networkTest = false;
            }
        }

        // 运行所有测试
        async function runAllTests() {
            log('开始运行所有测试...', 'success');
            clearLog();
            
            await checkAudioFile();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            checkAudioElements();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testVue();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testCompatibility();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testNetwork();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // 生成测试报告
            generateTestReport();
        }

        function generateTestReport() {
            log('=== 测试报告 ===', 'success');
            const passed = Object.values(testResults).filter(Boolean).length;
            const total = Object.keys(testResults).length;
            
            log(`通过: ${passed}/${total}`, passed === total ? 'success' : 'warning');
            
            Object.entries(testResults).forEach(([test, result]) => {
                log(`${test}: ${result ? '✅ 通过' : '❌ 失败'}`, result ? 'success' : 'error');
            });
            
            if (passed === total) {
                log('所有测试通过！音乐功能应该正常工作。', 'success');
            } else {
                log('部分测试失败，请查看具体错误信息。', 'warning');
            }
        }

        // 尝试自动修复
        function attemptFix() {
            log('尝试自动修复...', 'success');
            
            // 重新初始化音频
            if (testAudio) {
                testAudio.load();
                log('重新加载音频元素');
            }
            
            // 清除可能的错误状态
            testResults = {};
            
            // 重新运行测试
            setTimeout(() => {
                runAllTests();
            }, 1000);
        }

        // 页面加载完成后初始化
        window.addEventListener('load', init);
    </script>
</body>
</html>
