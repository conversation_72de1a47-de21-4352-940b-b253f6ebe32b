<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue.js 状态检查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            font-weight: bold;
            font-size: 18px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 2px solid #28a745;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #dc3545;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffc107;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 2px solid #17a2b8;
        }
        .test-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-btn:hover {
            background: #0056b3;
        }
        .code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: monospace;
            margin: 10px 0;
        }
        .vue-test {
            border: 2px solid #4fc08d;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            background: #f0f8f0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Vue.js 状态检查工具</h1>
        
        <div id="status" class="status info">正在检查 Vue.js 状态...</div>
        
        <div class="vue-test" id="vue-app">
            <h3>Vue.js 测试区域</h3>
            <p>当前计数: {{ count }}</p>
            <button @click="increment" class="test-btn">点击增加 (+1)</button>
            <button @click="testMusic" class="test-btn">测试音乐功能</button>
            <p v-if="musicStatus">音乐状态: {{ musicStatus }}</p>
        </div>
        
        <div>
            <h3>检查项目:</h3>
            <button class="test-btn" onclick="checkVueFromCDN()">检查CDN Vue.js</button>
            <button class="test-btn" onclick="checkVueFromLocal()">检查本地 Vue.js</button>
            <button class="test-btn" onclick="checkVueInstance()">检查Vue实例</button>
            <button class="test-btn" onclick="runAllChecks()">运行所有检查</button>
        </div>
        
        <div id="results"></div>
        
        <div>
            <h3>解决方案:</h3>
            <div class="code">
                <strong>如果Vue.js未加载，请尝试:</strong><br>
                1. 使用原生JS版本: <a href="index-no-vue.html">index-no-vue.html</a><br>
                2. 检查网络连接<br>
                3. 使用本地Vue.js文件<br>
                4. 清除浏览器缓存
            </div>
        </div>
        
        <div style="margin-top: 30px; text-align: center;">
            <a href="index.html">主应用 (Vue版)</a> | 
            <a href="index-no-vue.html">主应用 (原生JS版)</a> | 
            <a href="test-dashboard.html">测试仪表板</a>
        </div>
    </div>

    <!-- 尝试加载本地Vue.js -->
    <script src="js/vue.global.js"></script>
    
    <!-- 备用CDN -->
    <script>
        // 如果本地Vue.js加载失败，尝试CDN
        if (typeof Vue === 'undefined') {
            console.log('本地Vue.js加载失败，尝试CDN...');
            const script = document.createElement('script');
            script.src = 'https://unpkg.com/vue@3/dist/vue.global.js';
            script.onload = () => {
                console.log('CDN Vue.js加载成功');
                initVueApp();
            };
            script.onerror = () => {
                console.error('CDN Vue.js也加载失败');
                showError('Vue.js 完全加载失败');
            };
            document.head.appendChild(script);
        } else {
            console.log('本地Vue.js加载成功');
            initVueApp();
        }
    </script>

    <script>
        let vueApp = null;
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = message;
            resultsDiv.appendChild(div);
        }
        
        function showError(message) {
            updateStatus(message, 'error');
            addResult(message, 'error');
        }
        
        function initVueApp() {
            if (typeof Vue === 'undefined') {
                showError('Vue.js 未定义，无法初始化应用');
                return;
            }
            
            try {
                const { createApp, ref } = Vue;
                
                vueApp = createApp({
                    setup() {
                        const count = ref(0);
                        const musicStatus = ref('');
                        
                        const increment = () => {
                            count.value++;
                            console.log('Vue.js 响应式更新正常，计数:', count.value);
                        };
                        
                        const testMusic = async () => {
                            musicStatus.value = '正在测试...';
                            
                            const audio = document.createElement('audio');
                            audio.src = 'assets/audio/romantic-musi.mp3';
                            audio.volume = 0.1;
                            
                            try {
                                await audio.play();
                                musicStatus.value = '✅ 音乐播放成功';
                                setTimeout(() => {
                                    audio.pause();
                                    musicStatus.value = '✅ 音乐功能正常';
                                }, 1000);
                            } catch (error) {
                                musicStatus.value = `❌ 音乐播放失败: ${error.message}`;
                            }
                        };
                        
                        return {
                            count,
                            musicStatus,
                            increment,
                            testMusic
                        };
                    }
                }).mount('#vue-app');
                
                updateStatus('✅ Vue.js 应用初始化成功！', 'success');
                addResult('Vue.js 应用运行正常', 'success');
                
            } catch (error) {
                showError(`Vue.js 应用初始化失败: ${error.message}`);
            }
        }
        
        function checkVueFromCDN() {
            addResult('检查CDN Vue.js...', 'info');
            
            fetch('https://unpkg.com/vue@3/dist/vue.global.js')
                .then(response => {
                    if (response.ok) {
                        addResult('✅ CDN Vue.js 可访问', 'success');
                    } else {
                        addResult(`❌ CDN Vue.js 访问失败 (${response.status})`, 'error');
                    }
                })
                .catch(error => {
                    addResult(`❌ CDN Vue.js 网络错误: ${error.message}`, 'error');
                });
        }
        
        function checkVueFromLocal() {
            addResult('检查本地 Vue.js...', 'info');
            
            fetch('js/vue.global.js')
                .then(response => {
                    if (response.ok) {
                        addResult('✅ 本地 Vue.js 文件存在', 'success');
                        return response.text();
                    } else {
                        addResult(`❌ 本地 Vue.js 文件不存在 (${response.status})`, 'error');
                    }
                })
                .then(content => {
                    if (content && content.includes('Vue')) {
                        addResult('✅ 本地 Vue.js 文件内容正常', 'success');
                    } else {
                        addResult('❌ 本地 Vue.js 文件内容异常', 'error');
                    }
                })
                .catch(error => {
                    addResult(`❌ 本地 Vue.js 检查失败: ${error.message}`, 'error');
                });
        }
        
        function checkVueInstance() {
            addResult('检查Vue实例...', 'info');
            
            if (typeof Vue !== 'undefined') {
                addResult(`✅ Vue.js 已加载 (版本: ${Vue.version || '未知'})`, 'success');
                
                if (vueApp) {
                    addResult('✅ Vue应用实例存在', 'success');
                } else {
                    addResult('⚠️ Vue应用实例不存在', 'warning');
                }
            } else {
                addResult('❌ Vue.js 未加载', 'error');
            }
        }
        
        function runAllChecks() {
            document.getElementById('results').innerHTML = '';
            addResult('=== 开始全面检查 ===', 'info');
            
            checkVueFromLocal();
            setTimeout(() => checkVueFromCDN(), 500);
            setTimeout(() => checkVueInstance(), 1000);
            
            setTimeout(() => {
                addResult('=== 检查完成 ===', 'info');
            }, 1500);
        }
        
        // 页面加载完成后的初始检查
        window.addEventListener('load', () => {
            setTimeout(() => {
                if (typeof Vue !== 'undefined') {
                    updateStatus('✅ Vue.js 加载成功！', 'success');
                } else {
                    updateStatus('❌ Vue.js 加载失败！', 'error');
                }
            }, 1000);
        });
    </script>
</body>
</html>
