// Vue.js 3 Heart Confession App
const { createApp, ref, reactive, computed, onMounted, onUnmounted, nextTick } = Vue;

// 简化的心形渲染器
class SimpleHeartRenderer {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.animationFrame = null;
        this.scale = 1;
        this.init();
    }

    init() {
        this.setupCanvas();
        this.draw();
    }

    setupCanvas() {
        const size = Math.min(window.innerWidth - 40, 300);
        this.canvas.width = size;
        this.canvas.height = size;
        this.canvas.style.width = size + 'px';
        this.canvas.style.height = size + 'px';
    }

    draw() {
        const ctx = this.ctx;
        const width = this.canvas.width;
        const height = this.canvas.height;

        // 清除画布
        ctx.clearRect(0, 0, width, height);

        // 绘制心形
        ctx.save();
        ctx.translate(width / 2, height / 2);
        ctx.scale(this.scale, this.scale);

        // 心形路径
        ctx.beginPath();
        ctx.fillStyle = '#ff6b9d';

        const size = Math.min(width, height) * 0.3;
        for (let t = 0; t <= Math.PI * 2; t += 0.01) {
            const x = size * (16 * Math.pow(Math.sin(t), 3));
            const y = -size * (13 * Math.cos(t) - 5 * Math.cos(2 * t) - 2 * Math.cos(3 * t) - Math.cos(4 * t));

            if (t === 0) {
                ctx.moveTo(x / 16, y / 16);
            } else {
                ctx.lineTo(x / 16, y / 16);
            }
        }

        ctx.closePath();
        ctx.fill();

        // 添加阴影效果
        ctx.shadowColor = 'rgba(255, 107, 157, 0.5)';
        ctx.shadowBlur = 20;
        ctx.fill();

        ctx.restore();
    }

    animateClick() {
        // 简单的缩放动画
        this.scale = 1.2;
        this.draw();

        setTimeout(() => {
            this.scale = 1;
            this.draw();
        }, 300);
    }

    destroy() {
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
        }
    }
}

// 简化的配置对象
const APP_CONFIG = {
    APP_NAME: '心动告白',
    DEFAULT_MESSAGES: [
        "你知道吗？你的笑容是我见过最美的风景。",
        "每当我看到你，我的心跳都会不由自主地加速。",
        "和你在一起的每一刻，都让我感到无比幸福。",
        "你的温柔如春风，轻抚着我的心田。",
        "我想牵着你的手，走过人生的每一个春夏秋冬。",
        "我爱你，不只是今天，而是每一个明天。"
    ],
    ANIMATION: {
        HEART_EXPLOSION_DURATION: 600,
        TOAST_DURATION: 2000
    },
    SUCCESS_MESSAGES: {
        CUSTOM_MESSAGES_SAVED: '自定义消息已保存',
        DEFAULT_MESSAGES_RESTORED: '已恢复默认消息',
        LINK_COPIED: '链接已复制到剪贴板',
        APP_INSTALLED: '应用已添加到桌面'
    },
    ERROR_MESSAGES: {
        SHARE_FAILED: '分享失败，请手动复制链接',
        SCREENSHOT_FAILED: '请使用浏览器的截图功能',
        SAVE_FAILED: '保存失败，请重试'
    }
};

const HeartConfessionApp = {
    setup() {
        // 响应式数据
        const currentMessage = ref(0);
        const isAnimating = ref(false);
        const isMusicPlaying = ref(false);
        const canInstall = ref(false);
        const showSettingsModal = ref(false);
        const messageKey = ref(0);
        
        // 消息数据
        const messages = ref([...APP_CONFIG.DEFAULT_MESSAGES]);
        const customMessages = ref([...APP_CONFIG.DEFAULT_MESSAGES]);
        
        // Toast通知
        const toast = reactive({
            show: false,
            message: '',
            type: 'success'
        });
        
        // 模板引用
        const heartCanvas = ref(null);
        const bgMusic = ref(null);
        
        // 简化的心形渲染器
        let heartRenderer = null;

        // 计算属性
        const currentMessageText = computed(() => {
            return messages.value[currentMessage.value] || '准备好接受我的告白了吗？';
        });

        const heartPromptText = computed(() => {
            if (currentMessage.value === 0) {
                return '点击❤️开始告白';
            }
            const remaining = 6 - currentMessage.value;
            return remaining > 0 ? `继续点击❤️ (${remaining}次)` : '点击❤️重新开始';
        });

        // 方法
        const showToast = (message, type = 'success') => {
            toast.message = message;
            toast.type = type;
            toast.show = true;

            setTimeout(() => {
                toast.show = false;
            }, APP_CONFIG.ANIMATION.TOAST_DURATION);
        };

        const initializeHeartRenderer = async () => {
            try {
                await nextTick();
                if (heartCanvas.value) {
                    heartRenderer = new SimpleHeartRenderer(heartCanvas.value);
                }
                console.log('Heart renderer initialized');
            } catch (error) {
                console.error('Failed to initialize heart renderer:', error);
            }
        };

        const loadUserPreferences = () => {
            try {
                // 从localStorage加载自定义消息
                const saved = localStorage.getItem('heartapp_custom_messages');
                if (saved) {
                    const savedMessages = JSON.parse(saved);
                    if (savedMessages && savedMessages.length === 6) {
                        messages.value = [...savedMessages];
                        customMessages.value = [...savedMessages];
                    }
                }
                console.log('User preferences loaded');
            } catch (error) {
                console.error('Failed to load user preferences:', error);
            }
        };

        const saveUserPreferences = () => {
            try {
                localStorage.setItem('heartapp_custom_messages', JSON.stringify(messages.value));
            } catch (error) {
                console.error('Failed to save user preferences:', error);
            }
        };
        
        const handleHeartClick = async (event) => {
            if (isAnimating.value) return;

            isAnimating.value = true;

            // 创建简单的点击效果
            createClickEffect(event);

            // 心形动画
            if (heartRenderer) {
                heartRenderer.animateClick();
            }

            // 显示下一条消息
            showNextMessage();

            // 创建心形粒子效果
            createHeartParticles();

            setTimeout(() => {
                isAnimating.value = false;
            }, APP_CONFIG.ANIMATION.HEART_EXPLOSION_DURATION);
        };

        const createClickEffect = (event) => {
            if (!heartCanvas.value) return;

            const rect = heartCanvas.value.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            // 创建波纹效果
            const ripple = document.createElement('div');
            ripple.style.cssText = `
                position: absolute;
                left: ${rect.left + x - 25}px;
                top: ${rect.top + y - 25}px;
                width: 50px;
                height: 50px;
                border-radius: 50%;
                background: rgba(255, 107, 157, 0.6);
                transform: scale(0);
                pointer-events: none;
                z-index: 1000;
            `;

            document.body.appendChild(ripple);

            // 动画效果
            ripple.animate([
                { transform: 'scale(0)', opacity: 1 },
                { transform: 'scale(2)', opacity: 0 }
            ], {
                duration: 600,
                easing: 'ease-out'
            }).onfinish = () => {
                if (document.body.contains(ripple)) {
                    document.body.removeChild(ripple);
                }
            };
        };

        const createHeartParticles = () => {
            if (!heartCanvas.value) return;

            const rect = heartCanvas.value.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;

            // 创建多个心形粒子
            for (let i = 0; i < 6; i++) {
                const particle = document.createElement('div');
                particle.textContent = '❤️';
                particle.style.cssText = `
                    position: absolute;
                    left: ${centerX}px;
                    top: ${centerY}px;
                    font-size: 1.5rem;
                    pointer-events: none;
                    z-index: 1000;
                `;

                document.body.appendChild(particle);

                const angle = (i / 6) * Math.PI * 2;
                const distance = 100;
                const endX = centerX + Math.cos(angle) * distance;
                const endY = centerY + Math.sin(angle) * distance - 50;

                particle.animate([
                    { transform: 'translate(-50%, -50%) scale(1)', opacity: 1 },
                    { transform: `translate(${endX - centerX}px, ${endY - centerY}px) scale(0.5)`, opacity: 0 }
                ], {
                    duration: 2000,
                    easing: 'ease-out'
                }).onfinish = () => {
                    if (document.body.contains(particle)) {
                        document.body.removeChild(particle);
                    }
                };
            }
        };
        
        const showNextMessage = () => {
            // 更新当前消息索引
            currentMessage.value = (currentMessage.value + 1) % messages.value.length;

            // 强制重新渲染消息文本
            messageKey.value++;
        };

        const toggleMusic = async () => {
            try {
                const audio = bgMusic.value;
                if (!audio) {
                    showToast('音频不可用', 'error');
                    return;
                }

                if (isMusicPlaying.value) {
                    audio.pause();
                    isMusicPlaying.value = false;
                    showToast('音乐已关闭', 'success');
                } else {
                    await audio.play();
                    isMusicPlaying.value = true;
                    showToast('音乐已开启', 'success');
                }
            } catch (error) {
                console.error('Music toggle failed:', error);
                showToast('音乐播放失败', 'error');
            }
        };
        
        const shareApp = async () => {
            try {
                if (navigator.share) {
                    await navigator.share({
                        title: APP_CONFIG.APP_NAME,
                        text: '用这个浪漫的告白应用表达你的心意吧！',
                        url: window.location.href
                    });
                } else {
                    // Fallback: copy to clipboard
                    await navigator.clipboard.writeText(window.location.href);
                    showToast(APP_CONFIG.SUCCESS_MESSAGES.LINK_COPIED, 'success');
                }
            } catch (error) {
                console.log('Share failed:', error);
                showToast(APP_CONFIG.ERROR_MESSAGES.SHARE_FAILED, 'error');
            }
        };

        const installApp = async () => {
            try {
                // 简化的PWA安装
                if (window.deferredPrompt) {
                    window.deferredPrompt.prompt();
                    const { outcome } = await window.deferredPrompt.userChoice;
                    if (outcome === 'accepted') {
                        showToast(APP_CONFIG.SUCCESS_MESSAGES.APP_INSTALLED, 'success');
                    }
                    window.deferredPrompt = null;
                    canInstall.value = false;
                } else {
                    showToast('请使用浏览器的"添加到主屏幕"功能', 'warning');
                }
            } catch (error) {
                console.error('Install failed:', error);
                showToast('安装失败，请重试', 'error');
            }
        };
        
        const takeScreenshot = () => {
            // 显示闪光效果
            const flash = document.createElement('div');
            flash.className = 'screenshot-flash';
            document.body.appendChild(flash);
            
            setTimeout(() => {
                document.body.removeChild(flash);
            }, 300);
            
            // 使用html2canvas截图（如果可用）
            if (typeof html2canvas !== 'undefined') {
                html2canvas(document.body).then(canvas => {
                    const link = document.createElement('a');
                    link.download = `heart-confession-${Date.now()}.png`;
                    link.href = canvas.toDataURL();
                    link.click();
                    showToast('截图已保存', 'success');
                }).catch(error => {
                    console.error('Screenshot failed:', error);
                    showToast(APP_CONFIG.ERROR_MESSAGES.SCREENSHOT_FAILED, 'error');
                });
            } else {
                showToast(APP_CONFIG.ERROR_MESSAGES.SCREENSHOT_FAILED, 'error');
            }
        };
        
        const openSettings = () => {
            // 加载当前消息到自定义消息数组
            customMessages.value = [...messages.value];
            showSettingsModal.value = true;
        };
        
        const closeSettings = () => {
            showSettingsModal.value = false;
        };
        
        const closeModalOnBackdrop = (event) => {
            if (event.target.classList.contains('modal')) {
                closeSettings();
            }
        };
        
        const getPlaceholder = (index) => {
            const placeholders = [
                '友好的赞美...',
                '温暖的关心...',
                '真诚的欣赏...',
                '深情的表达...',
                '浪漫的承诺...',
                '深情的告白...'
            ];
            return placeholders[index] || '输入告白语句...';
        };
        
        const saveCustomMessages = () => {
            try {
                const hasCustom = customMessages.value.some((msg, index) =>
                    msg.trim() !== '' && msg !== APP_CONFIG.DEFAULT_MESSAGES[index]
                );

                if (hasCustom) {
                    // 填充空消息为默认消息
                    const finalMessages = customMessages.value.map((msg, index) =>
                        msg.trim() || APP_CONFIG.DEFAULT_MESSAGES[index]
                    );

                    messages.value = [...finalMessages];
                    saveUserPreferences();
                    showToast(APP_CONFIG.SUCCESS_MESSAGES.CUSTOM_MESSAGES_SAVED, 'success');
                    closeSettings();
                } else {
                    showToast('请至少修改一条消息', 'warning');
                }
            } catch (error) {
                console.error('Save failed:', error);
                showToast(APP_CONFIG.ERROR_MESSAGES.SAVE_FAILED, 'error');
            }
        };

        const resetToDefault = () => {
            customMessages.value = [...APP_CONFIG.DEFAULT_MESSAGES];
            messages.value = [...APP_CONFIG.DEFAULT_MESSAGES];
            try {
                localStorage.removeItem('heartapp_custom_messages');
            } catch (error) {
                console.error('Failed to remove custom messages:', error);
            }
            showToast(APP_CONFIG.SUCCESS_MESSAGES.DEFAULT_MESSAGES_RESTORED, 'success');
        };
        
        // 生命周期钩子
        onMounted(async () => {
            await initializeHeartRenderer();
            loadUserPreferences();

            // 检查PWA安装状态
            window.addEventListener('beforeinstallprompt', (e) => {
                e.preventDefault();
                window.deferredPrompt = e;
                canInstall.value = true;
            });

            console.log('Heart Confession Vue App mounted successfully');
        });

        onUnmounted(() => {
            // 清理资源
            if (heartRenderer && typeof heartRenderer.destroy === 'function') {
                heartRenderer.destroy();
            }
            console.log('Heart Confession Vue App unmounted');
        });
        
        // 返回模板需要的数据和方法
        return {
            // 响应式数据
            currentMessage,
            isAnimating,
            isMusicPlaying,
            canInstall,
            showSettingsModal,
            messageKey,
            messages,
            customMessages,
            toast,
            
            // 模板引用
            heartCanvas,
            bgMusic,
            
            // 计算属性
            currentMessageText,
            heartPromptText,
            
            // 方法
            handleHeartClick,
            toggleMusic,
            shareApp,
            installApp,
            takeScreenshot,
            openSettings,
            closeSettings,
            closeModalOnBackdrop,
            getPlaceholder,
            saveCustomMessages,
            resetToDefault
        };
    }
};

// 创建并挂载Vue应用
createApp(HeartConfessionApp).mount('#app');

console.log('Heart Confession Vue.js 3 App initialized successfully');
