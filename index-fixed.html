<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="心动告白 - 一个浪漫的告白应用">
    <meta name="theme-color" content="#ff6b9d">
    <title>心动告白 - Heart Confession (修复版)</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/style.css">
    
    <!-- Vue.js 3 CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
</head>
<body>
    <div id="app">
        <div class="app-container">
            <!-- Header -->
            <header class="header">
                <h1 class="app-title">心动告白 (修复版)</h1>
                <div class="progress-indicator">
                    <span>{{ currentMessage }}/6</span>
                </div>
            </header>
            
            <!-- Main content -->
            <main class="main-content">
                <!-- Heart container -->
                <div class="heart-container">
                    <canvas 
                        ref="heartCanvas" 
                        id="heart-canvas" 
                        width="300" 
                        height="300"
                        @click="handleHeartClick"
                        :class="{ 'heart-explode': isAnimating }"
                    ></canvas>
                    <div class="heart-prompt">
                        <span>{{ heartPromptText }}</span>
                    </div>
                </div>
                
                <!-- Message display -->
                <div class="message-container">
                    <div class="message-box">
                        <p class="message-text" :key="messageKey">{{ currentMessageText }}</p>
                    </div>
                </div>
                
                <!-- Action buttons -->
                <div class="action-buttons">
                    <button @click="toggleMusic" class="action-btn" :class="{ 'music-active': isMusicPlaying }">
                        <span>{{ isMusicPlaying ? '🎵' : '🔇' }}</span>
                        <span>{{ isMusicPlaying ? '播放中' : '音乐' }}</span>
                    </button>
                    <button @click="shareApp" class="action-btn">
                        <span>📱</span>
                        <span>分享</span>
                    </button>
                    <button @click="takeScreenshot" class="action-btn">
                        <span>📸</span>
                        <span>截图</span>
                    </button>
                </div>
            </main>
            
            <!-- Toast notification -->
            <div v-if="toast.show" class="toast" :class="toast.type">
                {{ toast.message }}
            </div>
            
            <!-- Debug panel -->
            <div style="position: fixed; top: 10px; right: 10px; background: rgba(0,0,0,0.8); color: white; padding: 10px; border-radius: 5px; font-size: 12px; z-index: 9999;">
                <div>🎵 {{ isMusicPlaying ? '播放中' : '已暂停' }}</div>
                <div>版本: Fixed v1.0</div>
                <button @click="debugAudio" style="margin-top: 5px; padding: 2px 8px; font-size: 10px;">调试音频</button>
            </div>
        </div>
    </div>
    
    <!-- Background audio -->
    <audio ref="bgMusic" id="bg-music" loop preload="metadata">
        <source src="assets/audio/romantic-musi.mp3" type="audio/mpeg">
        <source src="assets/audio/romantic-musi.mp3" type="audio/mp3">
        您的浏览器不支持音频播放。
    </audio>

    <script>
        const { createApp, ref, reactive, computed, onMounted, nextTick } = Vue;

        // 简化的心形渲染器
        class SimpleHeartRenderer {
            constructor(canvas) {
                this.canvas = canvas;
                this.ctx = canvas.getContext('2d');
                this.scale = 1;
                this.init();
            }
            
            init() {
                this.setupCanvas();
                this.draw();
            }
            
            setupCanvas() {
                const size = Math.min(window.innerWidth - 40, 300);
                this.canvas.width = size;
                this.canvas.height = size;
                this.canvas.style.width = size + 'px';
                this.canvas.style.height = size + 'px';
            }
            
            draw() {
                const ctx = this.ctx;
                const width = this.canvas.width;
                const height = this.canvas.height;
                
                ctx.clearRect(0, 0, width, height);
                ctx.save();
                ctx.translate(width / 2, height / 2);
                ctx.scale(this.scale, this.scale);
                
                ctx.beginPath();
                ctx.fillStyle = '#ff6b9d';
                
                const size = Math.min(width, height) * 0.3;
                for (let t = 0; t <= Math.PI * 2; t += 0.01) {
                    const x = size * (16 * Math.pow(Math.sin(t), 3));
                    const y = -size * (13 * Math.cos(t) - 5 * Math.cos(2 * t) - 2 * Math.cos(3 * t) - Math.cos(4 * t));
                    
                    if (t === 0) {
                        ctx.moveTo(x / 16, y / 16);
                    } else {
                        ctx.lineTo(x / 16, y / 16);
                    }
                }
                
                ctx.closePath();
                ctx.fill();
                ctx.shadowColor = 'rgba(255, 107, 157, 0.5)';
                ctx.shadowBlur = 20;
                ctx.fill();
                ctx.restore();
            }
            
            animateClick() {
                this.scale = 1.2;
                this.draw();
                setTimeout(() => {
                    this.scale = 1;
                    this.draw();
                }, 300);
            }
        }

        // 应用配置
        const APP_CONFIG = {
            DEFAULT_MESSAGES: [
                "你知道吗？你的笑容是我见过最美的风景。",
                "每当我看到你，我的心跳都会不由自主地加速。",
                "和你在一起的每一刻，都让我感到无比幸福。",
                "你的温柔如春风，轻抚着我的心田。",
                "我想牵着你的手，走过人生的每一个春夏秋冬。",
                "我爱你，不只是今天，而是每一个明天。"
            ],
            ANIMATION: {
                HEART_EXPLOSION_DURATION: 600,
                TOAST_DURATION: 2000
            }
        };

        const HeartConfessionApp = {
            setup() {
                // 响应式数据
                const currentMessage = ref(0);
                const isAnimating = ref(false);
                const isMusicPlaying = ref(false);
                const messageKey = ref(0);
                const messages = ref([...APP_CONFIG.DEFAULT_MESSAGES]);
                
                // Toast通知
                const toast = reactive({
                    show: false,
                    message: '',
                    type: 'success'
                });
                
                // 模板引用
                const heartCanvas = ref(null);
                const bgMusic = ref(null);
                
                // 心形渲染器
                let heartRenderer = null;
                
                // 计算属性
                const currentMessageText = computed(() => {
                    return messages.value[currentMessage.value] || '准备好接受我的告白了吗？';
                });
                
                const heartPromptText = computed(() => {
                    if (currentMessage.value === 0) {
                        return '点击❤️开始告白';
                    }
                    const remaining = 6 - currentMessage.value;
                    return remaining > 0 ? `继续点击❤️ (${remaining}次)` : '点击❤️重新开始';
                });
                
                // 方法
                const showToast = (message, type = 'success') => {
                    toast.message = message;
                    toast.type = type;
                    toast.show = true;
                    
                    setTimeout(() => {
                        toast.show = false;
                    }, APP_CONFIG.ANIMATION.TOAST_DURATION);
                };
                
                const toggleMusic = async () => {
                    console.log('🎵 toggleMusic called');
                    const audio = bgMusic.value;
                    
                    if (!audio) {
                        console.error('❌ Audio element not found');
                        showToast('音频元素未找到', 'error');
                        return;
                    }
                    
                    try {
                        if (isMusicPlaying.value) {
                            console.log('⏸️ Pausing music');
                            audio.pause();
                            isMusicPlaying.value = false;
                            showToast('音乐已暂停', 'success');
                        } else {
                            console.log('▶️ Playing music');
                            audio.volume = 0.3;
                            await audio.play();
                            isMusicPlaying.value = true;
                            showToast('音乐已开启', 'success');
                        }
                    } catch (error) {
                        console.error('❌ Music toggle error:', error);
                        showToast(`播放失败: ${error.message}`, 'error');
                    }
                };
                
                const handleHeartClick = async (event) => {
                    if (isAnimating.value) return;
                    
                    // 第一次点击时尝试播放音乐
                    if (currentMessage.value === 0 && !isMusicPlaying.value) {
                        await toggleMusic();
                    }
                    
                    isAnimating.value = true;
                    
                    if (heartRenderer) {
                        heartRenderer.animateClick();
                    }
                    
                    // 显示下一条消息
                    currentMessage.value = (currentMessage.value + 1) % messages.value.length;
                    messageKey.value++;
                    
                    setTimeout(() => {
                        isAnimating.value = false;
                    }, APP_CONFIG.ANIMATION.HEART_EXPLOSION_DURATION);
                };
                
                const shareApp = async () => {
                    try {
                        if (navigator.share) {
                            await navigator.share({
                                title: '心动告白',
                                text: '用这个浪漫的告白应用表达你的心意吧！',
                                url: window.location.href
                            });
                        } else {
                            await navigator.clipboard.writeText(window.location.href);
                            showToast('链接已复制到剪贴板', 'success');
                        }
                    } catch (error) {
                        showToast('分享失败', 'error');
                    }
                };
                
                const takeScreenshot = () => {
                    const flash = document.createElement('div');
                    flash.className = 'screenshot-flash';
                    document.body.appendChild(flash);
                    
                    setTimeout(() => {
                        document.body.removeChild(flash);
                    }, 300);
                    
                    showToast('请使用浏览器的截图功能', 'warning');
                };
                
                const debugAudio = () => {
                    const audio = bgMusic.value;
                    console.log('🔧 Audio debug info:');
                    console.log('- Element:', audio);
                    console.log('- Src:', audio?.src);
                    console.log('- Paused:', audio?.paused);
                    console.log('- Volume:', audio?.volume);
                    console.log('- ReadyState:', audio?.readyState);
                    console.log('- NetworkState:', audio?.networkState);
                    
                    showToast(`音频调试: ${audio ? '元素存在' : '元素不存在'}`, audio ? 'success' : 'error');
                };
                
                // 生命周期
                onMounted(async () => {
                    await nextTick();
                    
                    // 初始化心形渲染器
                    if (heartCanvas.value) {
                        heartRenderer = new SimpleHeartRenderer(heartCanvas.value);
                    }
                    
                    // 初始化音频
                    const audio = bgMusic.value;
                    if (audio) {
                        audio.volume = 0.3;
                        
                        audio.addEventListener('play', () => {
                            console.log('🎵 Audio play event');
                            isMusicPlaying.value = true;
                        });
                        
                        audio.addEventListener('pause', () => {
                            console.log('⏸️ Audio pause event');
                            isMusicPlaying.value = false;
                        });
                        
                        audio.addEventListener('error', (e) => {
                            console.error('❌ Audio error:', e);
                            showToast('音频加载失败', 'error');
                        });
                        
                        console.log('✅ Audio initialized');
                    }
                    
                    console.log('✅ App mounted successfully');
                });
                
                return {
                    currentMessage,
                    isAnimating,
                    isMusicPlaying,
                    messageKey,
                    messages,
                    toast,
                    heartCanvas,
                    bgMusic,
                    currentMessageText,
                    heartPromptText,
                    handleHeartClick,
                    toggleMusic,
                    shareApp,
                    takeScreenshot,
                    debugAudio
                };
            }
        };

        // 创建并挂载Vue应用
        createApp(HeartConfessionApp).mount('#app');
        console.log('🚀 Heart Confession App (Fixed) initialized');
    </script>
</body>
</html>
