# 心动告白 - Heart Confession App (Vue.js 3版本)

一个基于Vue.js 3开发的浪漫告白移动端响应式网页应用。

## ✨ 功能特性

### 🎯 核心功能
- **交互式心形动画** - 点击心形触发炫酷的爆炸和粒子效果
- **递进式告白系统** - 6句精心设计的告白语句，情感强度递进
- **移动端优化** - 完美适配iOS Safari、Android Chrome等移动浏览器
- **响应式设计** - 自适应不同屏幕尺寸，从手机到平板都完美显示

### 🎨 视觉效果
- **渐变背景** - 浪漫的粉色到金色渐变背景
- **心形渲染** - 使用Canvas绘制的精美心形图案
- **动画效果** - 点击波纹、心形粒子、缩放动画等丰富效果
- **现代UI** - 毛玻璃效果、阴影、圆角等现代设计元素

### 🔧 实用功能
- **音乐播放** - 支持背景音乐播放控制
- **分享功能** - 支持原生分享API和链接复制
- **截图功能** - 一键截图保存美好瞬间
- **自定义告白** - 可自定义6句告白语句
- **PWA支持** - 可安装到桌面，离线使用

## 🛠 技术栈

### 前端框架
- **Vue.js 3** - 使用Composition API构建响应式应用
- **原生JavaScript** - ES6+语法，模块化开发

### 核心技术
- **HTML5 Canvas** - 心形渲染和动画效果
- **CSS3** - 现代样式、动画和响应式布局
- **Web APIs** - 分享、剪贴板、PWA等现代浏览器API
- **LocalStorage** - 本地数据持久化

### 动画库
- **GSAP** - 高性能动画库
- **CSS Animations** - 原生CSS动画
- **Web Animations API** - 现代动画API

## 🚀 快速开始

### 环境要求
- 现代浏览器（Chrome 80+, Firefox 75+, Safari 13+, Edge 80+）
- 本地Web服务器（推荐）

### 安装运行

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd heart-confession-app
   ```

2. **启动本地服务器**
   ```bash
   # 使用Python
   python -m http.server 8009

   # 或使用Node.js
   npx serve . -p 8009

   # 或使用PHP
   php -S localhost:8009
   ```

3. **访问应用**
   打开浏览器访问 `http://localhost:8009`

### 快速启动
**Windows用户：**
- 双击 `start-server.bat` 文件即可启动服务器

**Mac/Linux用户：**
- 运行 `chmod +x start-server.sh` 给脚本执行权限
- 运行 `./start-server.sh` 启动服务器

### 直接使用
也可以直接双击 `index.html` 文件在浏览器中打开，但推荐使用本地服务器以获得最佳体验。

## 📁 项目结构

```
heart-confession-app/
├── index.html              # 主HTML文件（Vue.js 3版本）
├── test.html              # Vue.js测试页面
├── start-server.bat       # Windows快速启动脚本
├── start-server.sh        # Mac/Linux快速启动脚本
├── css/
│   └── style.css          # 样式文件（包含响应式设计）
├── js/
│   ├── vue-app.js         # Vue.js 3主应用文件
│   ├── app.js             # 原版JavaScript应用（备用）
│   └── modules/           # 模块化组件
│       ├── config.js      # 应用配置
│       ├── HeartRenderer.js    # 心形渲染器
│       ├── AnimationManager.js # 动画管理器
│       ├── AudioManager.js     # 音频管理器
│       ├── StorageManager.js   # 存储管理器
│       ├── UIManager.js        # UI管理器
│       ├── PWAManager.js       # PWA管理器
│       └── PerformanceManager.js # 性能管理器
├── assets/                # 资源文件
│   ├── audio/            # 音频文件
│   └── icon-placeholder.svg # 图标文件
├── manifest.json         # PWA清单文件
├── sw.js                # Service Worker
└── README.md            # 项目说明文档
```

## 🎮 使用说明

### 基本操作
1. **开始告白** - 点击中央的心形图案
2. **查看消息** - 每次点击显示一句告白语句
3. **循环播放** - 6句话播放完后重新开始
4. **音乐控制** - 点击音乐按钮开启/关闭背景音乐

### 高级功能
1. **自定义告白** - 点击右上角设置按钮自定义告白语句
2. **分享应用** - 点击分享按钮分享给朋友
3. **安装应用** - 支持PWA安装到桌面
4. **截图保存** - 点击截图按钮保存当前画面

### 告白语句设计
默认的6句告白语句按情感强度递进：
1. **友好赞美** - "你知道吗？你的笑容是我见过最美的风景。"
2. **情感表达** - "每当我看到你，我的心跳都会不由自主地加速。"
3. **幸福感受** - "和你在一起的每一刻，都让我感到无比幸福。"
4. **温柔比喻** - "你的温柔如春风，轻抚着我的心田。"
5. **未来承诺** - "我想牵着你的手，走过人生的每一个春夏秋冬。"
6. **深情告白** - "我爱你，不只是今天，而是每一个明天。"

## 🎨 自定义配置

### 修改告白语句
1. 点击右上角的设置按钮（⚙️）
2. 在弹出的对话框中修改对应的语句
3. 点击"保存"按钮保存自定义内容
4. 点击"恢复默认"可以重置为原始语句

### 主题配置
应用支持多种主题色彩，可在 `js/modules/config.js` 中配置：
- **浪漫粉** - 默认主题
- **夕阳橙** - 温暖橙色主题
- **海洋蓝** - 清新蓝色主题
- **森林绿** - 自然绿色主题

## 📱 移动端优化

### 响应式设计
- **自适应布局** - 根据屏幕尺寸自动调整
- **触摸优化** - 针对触摸操作优化的按钮大小
- **性能优化** - 根据设备性能调整动画效果

### 兼容性测试
- ✅ iOS Safari 13+
- ✅ Android Chrome 80+
- ✅ Samsung Internet
- ✅ Firefox Mobile
- ✅ Edge Mobile

## 🔧 开发说明

### Vue.js 3特性使用
- **Composition API** - 使用setup()函数组织逻辑
- **响应式系统** - ref()和reactive()管理状态
- **生命周期钩子** - onMounted()和onUnmounted()
- **计算属性** - computed()实现派生状态
- **模板引用** - ref获取DOM元素

### 性能优化
- **懒加载** - 按需加载资源
- **防抖节流** - 优化频繁操作
- **内存管理** - 及时清理动画和事件监听器
- **设备检测** - 根据设备性能调整功能

## 🌟 特色亮点

1. **技术先进** - 使用Vue.js 3最新特性
2. **用户体验** - 流畅的动画和直观的交互
3. **移动优先** - 专为移动设备优化设计
4. **可定制性** - 支持自定义告白内容
5. **现代化** - PWA支持，可安装使用
6. **浪漫主题** - 精心设计的视觉效果

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

---

💝 **用这个浪漫的告白应用表达你的心意吧！** 💝
