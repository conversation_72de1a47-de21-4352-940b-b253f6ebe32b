<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单音乐测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
            line-height: 1.6;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .big-button {
            display: block;
            width: 100%;
            padding: 20px;
            font-size: 24px;
            margin: 10px 0;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .play-btn {
            background: #28a745;
            color: white;
        }
        .play-btn:hover {
            background: #218838;
        }
        .pause-btn {
            background: #ffc107;
            color: black;
        }
        .pause-btn:hover {
            background: #e0a800;
        }
        .test-btn {
            background: #007bff;
            color: white;
        }
        .test-btn:hover {
            background: #0056b3;
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            font-weight: bold;
            font-size: 18px;
            text-align: center;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 2px solid #28a745;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #dc3545;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffc107;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 2px solid #17a2b8;
        }
        .console {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin: 15px 0;
        }
        .volume-control {
            margin: 20px 0;
            text-align: center;
        }
        .volume-slider {
            width: 300px;
            height: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: #333;">🎵 简单音乐测试</h1>
        
        <div id="status" class="status info">准备测试音乐功能...</div>
        
        <button class="big-button play-btn" onclick="playMusic()">
            ▶️ 播放音乐
        </button>
        
        <button class="big-button pause-btn" onclick="pauseMusic()">
            ⏸️ 暂停音乐
        </button>
        
        <button class="big-button test-btn" onclick="testEverything()">
            🔧 完整测试
        </button>
        
        <div class="volume-control">
            <label for="volume">音量控制:</label><br>
            <input type="range" id="volume" class="volume-slider" min="0" max="1" step="0.1" value="0.3" onchange="setVolume(this.value)">
            <br>
            <span id="volume-display">30%</span>
        </div>
        
        <div>
            <h3>实时控制台:</h3>
            <div class="console" id="console"></div>
            <button onclick="clearConsole()" style="padding: 5px 10px;">清除控制台</button>
        </div>
        
        <div style="margin-top: 30px; text-align: center;">
            <p><strong>音频文件:</strong> assets/audio/romantic-musi.mp3</p>
            <p><strong>测试说明:</strong> 点击播放按钮，如果听到音乐说明功能正常</p>
            <p><a href="index.html">← 返回主应用</a></p>
        </div>
    </div>

    <!-- 音频元素 -->
    <audio id="music" loop preload="metadata">
        <source src="assets/audio/romantic-musi.mp3" type="audio/mpeg">
        <source src="assets/audio/romantic-musi.mp3" type="audio/mp3">
        您的浏览器不支持音频播放。
    </audio>

    <script>
        const audio = document.getElementById('music');
        const statusDiv = document.getElementById('status');
        const consoleDiv = document.getElementById('console');
        
        let isPlaying = false;

        // 控制台日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: '#0f0',
                error: '#f00',
                warning: '#ff0',
                success: '#0f0'
            };
            
            consoleDiv.innerHTML += `<div style="color: ${colors[type] || '#0f0'}">[${timestamp}] ${message}</div>`;
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}]`, message);
        }

        function clearConsole() {
            consoleDiv.innerHTML = '';
        }

        function updateStatus(message, type = 'info') {
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            log(message, type);
        }

        // 播放音乐
        async function playMusic() {
            log('用户点击播放按钮');
            
            if (!audio) {
                updateStatus('❌ 音频元素不存在', 'error');
                return;
            }

            try {
                log('设置音量为30%');
                audio.volume = 0.3;
                
                log('尝试播放音频...');
                await audio.play();
                
                isPlaying = true;
                updateStatus('✅ 音乐正在播放！', 'success');
                log('音频播放成功！', 'success');
                
            } catch (error) {
                isPlaying = false;
                updateStatus(`❌ 播放失败: ${error.message}`, 'error');
                log(`播放失败: ${error.name} - ${error.message}`, 'error');
                
                // 提供解决建议
                if (error.name === 'NotAllowedError') {
                    log('建议: 浏览器阻止了自动播放，这是正常的安全限制', 'warning');
                } else if (error.name === 'NotSupportedError') {
                    log('建议: 音频格式不支持，请检查文件格式', 'error');
                }
            }
        }

        // 暂停音乐
        function pauseMusic() {
            log('用户点击暂停按钮');
            
            if (!audio) {
                updateStatus('❌ 音频元素不存在', 'error');
                return;
            }

            audio.pause();
            isPlaying = false;
            updateStatus('⏸️ 音乐已暂停', 'warning');
            log('音频已暂停');
        }

        // 设置音量
        function setVolume(value) {
            if (audio) {
                audio.volume = value;
                document.getElementById('volume-display').textContent = Math.round(value * 100) + '%';
                log(`音量设置为 ${Math.round(value * 100)}%`);
            }
        }

        // 完整测试
        async function testEverything() {
            log('=== 开始完整测试 ===', 'info');
            clearConsole();
            
            // 1. 检查音频元素
            log('1. 检查音频元素...');
            if (audio) {
                log('✅ 音频元素存在', 'success');
                log(`   - 源数量: ${audio.children.length}`);
                log(`   - 当前源: ${audio.currentSrc || '无'}`);
                log(`   - 网络状态: ${audio.networkState}`);
                log(`   - 就绪状态: ${audio.readyState}`);
            } else {
                log('❌ 音频元素不存在', 'error');
                return;
            }

            // 2. 检查音频文件
            log('2. 检查音频文件...');
            try {
                const response = await fetch('assets/audio/romantic-musi.mp3');
                if (response.ok) {
                    const blob = await response.blob();
                    const sizeMB = (blob.size / 1024 / 1024).toFixed(2);
                    log(`✅ 音频文件存在 (${sizeMB} MB)`, 'success');
                } else {
                    log(`❌ 音频文件不存在 (HTTP ${response.status})`, 'error');
                    return;
                }
            } catch (error) {
                log(`❌ 音频文件检查失败: ${error.message}`, 'error');
                return;
            }

            // 3. 检查浏览器支持
            log('3. 检查浏览器支持...');
            const mp3Support = audio.canPlayType('audio/mpeg');
            if (mp3Support) {
                log(`✅ 浏览器支持MP3: ${mp3Support}`, 'success');
            } else {
                log('❌ 浏览器不支持MP3格式', 'error');
                return;
            }

            // 4. 测试播放
            log('4. 测试播放功能...');
            try {
                audio.volume = 0.1; // 低音量测试
                await audio.play();
                log('✅ 播放测试成功', 'success');
                
                // 短暂播放后暂停
                setTimeout(() => {
                    audio.pause();
                    log('测试播放结束');
                    updateStatus('✅ 所有测试通过！音乐功能正常', 'success');
                }, 2000);
                
            } catch (error) {
                log(`❌ 播放测试失败: ${error.message}`, 'error');
                updateStatus(`❌ 播放测试失败: ${error.message}`, 'error');
            }

            log('=== 测试完成 ===', 'info');
        }

        // 音频事件监听
        if (audio) {
            audio.addEventListener('loadstart', () => log('开始加载音频'));
            audio.addEventListener('loadeddata', () => log('音频数据已加载'));
            audio.addEventListener('canplay', () => log('音频可以播放'));
            audio.addEventListener('canplaythrough', () => log('音频完全加载'));
            audio.addEventListener('play', () => {
                log('音频播放事件触发');
                isPlaying = true;
            });
            audio.addEventListener('pause', () => {
                log('音频暂停事件触发');
                isPlaying = false;
            });
            audio.addEventListener('error', (e) => {
                log(`音频错误事件: ${e.target.error?.message || '未知错误'}`, 'error');
            });
            audio.addEventListener('ended', () => {
                log('音频播放结束');
                isPlaying = false;
            });
        }

        // 页面加载完成
        window.addEventListener('load', () => {
            log('页面加载完成');
            updateStatus('🎵 准备就绪 - 点击播放按钮测试音乐', 'info');
            
            if (audio) {
                log(`音频元素初始化完成，源: ${audio.currentSrc || '未设置'}`);
            } else {
                log('警告: 音频元素未找到', 'error');
            }
        });
    </script>
</body>
</html>
